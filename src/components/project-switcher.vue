<template>
  <div class="project-switcher font-family-averta">
    <select-dropdown
      v-if="projectModel?.id"
      v-model="projectModel"
      :options="options"
      panelClasses="project-switcher-select-dropdown-panel"
      search-input-test-id="project-search-input"
      search-input-clear-test-id="exit-icon"
    />
  </div>
</template>

<script setup>
import { useNuxtApp } from '#app';
import { useStore } from "vuex";
import { useI18n } from "vue-i18n";
import SelectDropdown from "./select-dropdown.vue";
import ConfirmModal from "./modals/confirm-modal.vue";
import { CONFIRM_MODAL_TYPE } from "../models/confirm-modal.model.js";
import { useInnitAuthStore } from "../stores/innit-auth.js";

const props = defineProps({
  isCampaignModified: {
    type: Boolean,
  },
});

const { $keys } = useNuxtApp();
const store = useStore();
const { t } = useI18n();
const { switchProject } = useProjectLang();
const { isInnitAdmin } = useInnitAuthStore();
const { triggerLoading } = useCommonUtils();
const { openModal, closeModal } = useBaseModal({
  "ProjectSwitcherConfirmModal": {
    component: ConfirmModal,
    skipClickOutside: true,
    props: {
      modalType: CONFIRM_MODAL_TYPE.EXIT,
      title: t('DESCRIPTION_POPUP.EXIT_PAGE_POPUP'),
    },
  },
});

const projectModel = computed({
  get: () => {
    const project = store.getters["userData/getProject"];
    return {
      id: project?.id,
      label: project?.displayName,
    };
  },
  set: (newValue) => selectProjectAsync(newValue),
});
const options = computed(() => {
  const projectList = store.getters["userData/getProjectList"] || [];
  return projectList.map((item) => {
    return {
      id: item?.id,
      label: item?.displayName,
    };
  });
});

const goToIndexPage = async () => await navigateTo("");
const goToOverviewPage = async () => await navigateTo("/overview");

const changeProjectAsync = async ({ id, label }, isRoute) => {
  triggerLoading($keys.KEY_NAMES.ROUTE_LOADING, true);

  await goToIndexPage();

  triggerLoading($keys.KEY_NAMES.CAMPAIGN_MODIFIED, false);
  triggerLoading($keys.KEY_NAMES.PROJECT_CHANGED);

  try {
    await switchProject({
      id: id,
      displayName: label
    });
    await store.dispatch('userData/fetchProjectsAsync', {
      isHotRefresh: true,
      isAdmin: isInnitAdmin.value,
    });

    await goToOverviewPage();
  } catch (error) {
    console.error("[IQ][ProjectSwitcher] Error in changeProjectAsync", error);
    await goToOverviewPage();
  } finally {
    triggerLoading($keys.KEY_NAMES.ROUTE_LOADING, false);
  }
};

const selectProjectAsync = async (data) => {
  if (props.isCampaignModified) {
    openModal({
      name: "ProjectSwitcherConfirmModal",
      onClose: async (response) => response && await changeProjectAsync(data),
    });
    return;
  }

  await changeProjectAsync(data);
};
</script>

