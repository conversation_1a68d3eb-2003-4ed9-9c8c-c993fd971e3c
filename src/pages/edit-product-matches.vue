<template>
<client-only>
  <content-wrapper
    :class="{ 'popup-position': isFilterBrandPopupVisible }"
    :is-body-loading="isPageLoading"
  >
    <div class="main-inner-section">
      <div class="about-first-edit-ingredient-products" v-if="!isPageLoading" id="productTable">
        <button type="button" class="back-to-ingredient-btn btn-reset" @click="backToIngredients()">
          <img alt="arrow"
            class="back-arrow-image-edit-ingredient-products"
            src="~/assets/images/back-arrow.png"
          />
          <span class="back-to-ingredient text-title-2">{{  $t('SWITCH_PAGES.BACK_TO_INGREDIENT') }}</span>
        </button>
        <div class="form-title" v-if="isAdminCheck">
          <p class="form-title-header text-title-2">
            {{ $t('LOCK_INGREDIENT') }}
          </p>
          <label
            class="switch simple-data-tooltip"
            :data-tooltip-text="isOnlyIncluded ? lockedCampaignTooltip : unlockedCampaignTooltip"
          >
            {{ isOnlyIncluded }}k
            <input :checked="isOnlyIncluded" @click="lockIngredientToggleAsync()" type="checkbox"/>
            <span class="slider round">
            </span>
          </label>
        </div>
        <div class="main-btn-text-edit-ingredient-products">
          <div class="head-btn-edit-ingredient-products">
            <button type="button"
              class="btn-green"
              @click="displayPopup()"
              @keydown="preventEnterAndSpaceKeyPress($event)"
              :class="
                isCampaignModified && singularName
                  ? 'btn-green'
                  : 'disabled-button btn-green'
              "
            >
              {{ $t('BUTTONS.SAVE_BUTTON') }}
            </button>
            <button type="button"
              class="btn-green-outline"
              @click="backToIngredients()"
              @keydown="preventEnterAndSpaceKeyPress($event)"
            >
              {{ $t('BUTTONS.CANCEL_BUTTON') }}
            </button>
          </div>
        </div>
      </div>
      <div class="ingredient-name-container" v-show="!isPageLoading">
        <div class="ingredient-top-main">
          <div class="ingredient-header text-h5">
            {{ $t('INGREDIENT.INGREDIENT_NAME') }}:
          </div>
          <div  class="ingredient-name text-title-1">
            {{shortestIngredientName}}
          </div>
          <div v-if="isAdminCheck" class="edit-btn" @click="openEditIngredientPopup()">
            <img alt="edit icon"
              src="@/assets/images/edit_icon_black.png"
            />
          </div>
        </div>
        <div class="shoppable-drop-down"   @click="showShoppableData()">
          <div class="shoppable-drop-down-container">
            <div class="shoppable-container">
              <img alt=""
                class="shoppable-drop-down-image"
                src="@/assets/images/shopping-cart-icon-green.png"
              />
              <div class="shoppable-drop-down-data text-title-2 font-normal">
              {{selectedShoppableName}}
              </div>
            </div>
            <img alt=""
              class="shoppable-dropdown-icon"
              src="@/assets/images/arrow-right.png"
              :class="{
                rotate: shoppableDropdownResult,
              }"
            />
          </div>
          <ul v-if="shoppableDropdownResult" class="autocomplete-results">
            <div>
              <li
                v-for="result in shoppableDataList"
                :key="result.data"
                :class="{
                  'autocomplete-result': true,
                  'is-active': result && result.data && (result.data == selectedShoppableName),
                }"
                @click="selectShoppableValue(result.data)"
              >
                {{ result.data }}
              </li>
            </div>
          </ul>
        </div>
        <div>
          <div class="view-all-main-container">
            <button type="button" @click="ingredientNameListPopupAsync()" class="view-all-text btn-reset">{{ $t('COMMON.VIEW_ALL') }}</button>
          </div>
          <hr class="hr-line">
        </div>
        <!-- Error message for ingredient not found -->
        <div v-if="isIngredientNotFoundError && !isPageLoading" class="ingredient-not-found-error">
          <div class="error-message-container">
            <img alt="warning" class="error-icon" src="@/assets/images/attention.svg">
            <span class="error-message-text">{{ ingredientNotFoundMessage }}</span>
            <img alt="close" class="error-close-icon" src="@/assets/images/exit-gray.png" @click="dismissErrorMessage">
          </div>
        </div>
        <div class="text-overwrites" v-if="totalRecipes!=0 && !isPageLoading">Changes to this form will NOT be applied to recipes with overwrites/overrides, indicated by <img alt="" class="info-icon" src="@/assets/images/informationSymbol.png">.</div>
      </div>
      <div class="recipes-main-container" v-if="totalRecipes!=0 && !isPageLoading">
        <div class="recipes-sub-container">
          <div class="recipes-top-section">
            <div class="recipes-text-section">
              <span v-if="totalRecipes > 1" class="recipe-count text-title-2">{{totalRecipes}} recipes:</span>
              <span v-if="totalRecipes <= 1" class="recipe-count text-title-2">{{totalRecipes}} recipe:</span>
            </div>
          </div>
          <div class="recipes-middle-section">
            <div class="recipes-container">
              <div
                  class="recipe-page-prev prev-button-recipe"
                  @click="setRecipePage(recipePage - 1)"
                  :class="recipePage == 0 ? 'disabled' : ''"
              >
              </div>
              <div v-show="recipeDetailsList && recipeDetailsList.length > 0" v-for="(recipe, index) in recipeDetailsList" :key="`recipeDetailsList${index}`">
                <div
                  class="recipes-section"
                  @click="getRecipeAsync(recipe.isin)"
                  :class="recipe.state == 'publishing' ? 'loading-phase' : ''"
                >
                  <div class="loading-state-recipe" v-if="recipe.state == 'publishing'">
                    <div class="loader-image"></div>
                    <div class="loader-text">Recipe update<br>in progress…</div>
                  </div>
                  <div class="inner-recipe-card">
                    <div
                      class="recipe-card-exclamatory-image"
                      :class="recipe.state == 'publishing' ? 'loading-recipe-phase' : ''"
                      v-if="recipe.hasCampaign"
                    >
                      <div
                        class="tool-tip simple-data-tooltip"
                        :data-tooltip-text="recipeOverwriteTooltip"
                      >
                        <img alt="info" class="info-icon" src="@/assets/images/informationSymbol.png">
                      </div>
                    </div>
                    <div class="recipe-image" :class="{ 'loading-recipe-phase': recipe.state === 'publishing' }">
                      <img
                        :src="recipe?.externalImageUrl || recipe?.image || defaultImage"
                        :alt="recipe?.title || 'recipe'"
                        @error="$event.target.src = defaultImage"
                      />
                    </div>
                    <div
                      class="recipe-title text-h4"
                      v-show="recipe.state !== 'publishing'"
                      :class="{ 'simple-data-tooltip simple-data-tooltip-edge': isTitleTruncated[index] }"
                      :data-tooltip-text="isTitleTruncated[index] ? recipe?.title : ''"
                      :ref="el => setTitleRef(el, index)"
                    >
                      <p class="recipe-title-text">{{ recipe?.title }}</p>
                    </div>
                    <div class="recipe-id" v-show="recipe.state != 'publishing'">{{recipe.isin}}</div>
                  </div>
                </div>
              </div>
              <div v-show="recipeDetailsList && recipeDetailsList.length != 0 && recipeEmptyCount > 0" v-for="(recipe, index) in recipeEmptyCount" :key="`recipeEmptyCount${index}`">
                <div class="recipes-empty-section"></div>
              </div>
              <div v-show="recipeDetailsList && recipeDetailsList.length == 0">
                <div
                  class="recipes-section-While-loading"
                >
                   <div class="loading-content">
            <div class="content">
              <div class="input-loading">
                <div class="loader-image"></div>
              </div>
              <div class="loading-text">
                <p>{{ $t('LOADER.LOADING') }}</p>
              </div>
            </div>
          </div>
                </div>
              </div>
              <div
                class="recipe-page-next next-button-recipe"
                @click="setRecipePage(recipePage + 1)"
                :class="(recipePage * recipePageSize) + recipePageSize >= totalRecipes ? 'disabled' : ''"
              >
              </div>
            </div>
          </div>
          <div v-if="isDisplayShoppableReview" class="recipes-bottom-container">
            <span>Click on recipe card to open Shoppable Tool</span>
          </div>
        </div>
      </div>
      <div class="about-second-edit-ingredient-products" v-show="!isPageLoading">
        <div
          class="container-edit-ingredient-products"
          :style="ingredientProductStyle"
          :class="{ 'ingredient-products-filter': isFilterBrandPopupVisible }"
        >
          <div class="content-edit-ingredient-products">
            <div :id="isSelectionEnabled && ingredientProductMatchesList.length>4 ? 'add-margin':''" class="tabcontent-edit-ingredient-products" v-if="!isUpdating">
              <div v-show="selectedShoppableName == 'Not Shoppable'" class="not-shoppable-main-container">
                <div class="not-shoppable-content">
                  <span>This ingredient is not shoppable. Make shoppable to view products.</span>
                </div>
              </div>
              <div v-show="selectedShoppableName != 'Not Shoppable'" class="promoted-ingredient-main-container">
                <div id="promoted-matches-ingredient-head" class="promoted-matches-ingredient-head display-3">
                  {{promotedIngredientMatchData?.length}} Promoted Products
                </div>
                <div class="right-ingredient-section">
                  <div class="publish-btn">
                    <span :class="['text', { disable: promotedIngredientMatchData?.length === 0 }]">
                      {{ $t('SHOW_ONLY_PROMOTED_PRODUCTS') }}
                    </span>
                    <label
                      :class="[
                        'switch',
                        { 'simple-data-tooltip simple-data-tooltip-edge': promotedIngredientMatchData?.length === 0 }
                      ]"
                      :data-tooltip-text="promotedIngredientMatchData?.length === 0 ? $t('COMMON.PROMOTE_RECIPE_WARNING') : null"
                    >
                      <input
                        type="checkbox"
                        @click="toggleSwitch"
                        :disabled="promotedIngredientMatchData?.length === 0"
                        :checked="isShowOnlyPromotedProducts"
                      />
                      <span
                        :class="[
                          'slider-round',
                          { 'inactive-button': promotedIngredientMatchData?.length === 0 }
                        ]"
                      ></span>
                    </label>
                  </div>
                </div>
                <div class="promoted-matches-ingredient-text">
                  Promoted products are the first to be displayed as matches for
                  ingredients.
                </div>
              </div>
              <div v-show="selectedShoppableName != 'Not Shoppable'" class="promoted-matches-ingredient-details-table">
                <div  style="width: 100%" class="main-table-edit-ingredient-product">
                  <div class="product-table-ingredient-head">
                    <div class="product-ingredient-title">
                      <div class="product-table-srno"></div>
                      <div class="product-table-image-container"></div>
                      <div class="product-name">PRODUCT</div>
                      <div class="product-id">PRODUCT ID</div>
                      <div class="product-button-container"></div>
                      <div class="product-menu"></div>
                    </div>
                  </div>
                  <div class="ingredient-loader-section" v-show="isIngredientPromoteLoading">
                    <div class="content">
                      <div class="loading">
                        <div class="input-loading">
                          <div class="loader-image"></div>
                        </div>
                        <div class="loading-text">
                          <p>{{ $t('LOADER.LOADING') }}</p>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div
                    class="table-body-edit-ingredient-product"
                    v-if="!isIngredientPromoteLoading"
                  >
                    <draggable
                      class="draggable-list"
                      :list="promotedIngredientMatchData"
                      :group="{ name: 'my-group', pull: true, put: true }"
                      @change="handleDragPromoted"
                      ghost-class="hidden-list"
                      handle=".promoted-handle"
                      :scroll-sensitivity="200"
                      :force-fallback="true"
                      @start="isDrag = true"
                      @end="isDrag = false"
                      :id="!promotedIngredientMatchData.length ? 'add-padding': ''"
                    >
                      <div class="edit-ingredient-promoted-table"
                        v-for="(data, index) in promotedIngredientMatchData"
                        :key="`promotedIngredientMatchData${index}`"
                      >
                        <img class="promoted-handle" alt="" src="@/assets/images/drag-vertically.svg?skipsvgo=true" />
                        <div :id="isSelectionEnabled?'selection-enabled':''" class="promoted-table-srno">
                          {{index+1}}
                        </div>
                        <div class="promoted-table-image-container">
                          <div class="promoted-table-image">
                            <img alt=""
                              class="promoted-image"
                              :src="
                                data&&data.image&&data.image.url
                                  ? data.image.url
                                  : defaultImage
                              "
                            />
                          </div>
                        </div>
                        <div class="promoted-name text-h3">
                          {{ getProductName(data) }}
                        </div>
                        <div class="promoted-id text-h3 font-normal">
                          {{ getProductId(data) }}
                        </div>
                        <div class="promoted-button"></div>
                        <div class="promoted-menu">
                          <div
                            :class="
                              data.dropDown
                                ? 'menu-container menu-selected'
                                : 'menu-container'
                            "
                            @click="displayOption(data)"
                          >
                            <img alt=""
                              v-if="data.dropDown"
                              class="table-edit-btn"
                              src="~/assets/images/green-edit-btn.svg?skipsvgo=true"
                            />
                            <img alt=""
                              v-if="!data.dropDown"
                              class="table-edit-btn"
                              src="~/assets/images/edit-btn.svg?skipsvgo=true"
                            />
                            <div class="menu-box" v-if="data.dropDown">
                              <ul class="menu-list">
                                <li @click="unPromoteProduct(data,index)">{{ $t('COMMON.UNPROMOTE') }}</li>
                              </ul>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div
                        class="zero-promoted-matches-ingredient-table"
                        v-if="!promotedIngredientMatchData.length"
                      >
                        0 promoted products.
                        <span>
                          Products will be auto-selected based on match rating.
                        </span>
                      </div>
                    </draggable>
                  </div>
                </div>
              </div>
              <div id="scrollUp" class="filter-search-section">
                <div class="table-head-edit-ingredient-products">
                  <div v-if="!isSearchMode" :class="(selectedShoppableName == 'Not Shoppable')? 'total-product-counting display-3 disable-counting' : 'total-product-counting display-3'">
                    {{ productTotal }} Product Matches
                  </div>
                  <div v-if="isSearchMode" :class="(selectedShoppableName == 'Not Shoppable')? 'total-product-counting display-3 disable-counting' : 'total-product-counting display-3'">
                    Search results
                  </div>
                  <div v-show="shouldShowFilterSection"
                    class="filter-section"
                    :class="{'disable-content': isDataLoading}"
                  >
                    <div class="filter-zone-section">
                      <div class="filter-icon">
                          <img alt="" src="@/assets/images/filter-icon.png" />
                      </div>
                      <div class="filter-text">Filter</div>
                    </div>
                    <div class="search-brand-popup">
                      <div
                        @click="setFilterBrandPopupVisible()"
                        class="brand-details"
                        v-show="countBrand > 0 && !isSearchMode"
                      >
                        <span class="brand-title">Brand</span>
                        <span
                          v-if="displayBrandName != ''"
                          class="brand-selected"
                          >{{ displayBrandName }}</span
                        >
                        <span
                          v-if="displayBrandName == ''"
                          class="brand-selected"
                          >All</span
                        >
                        <img alt=""
                          src="@/assets/images/arrow-down-green.png"
                          class="brand-dropdown-icon"
                          :class="{
                            rotate:
                              isFilterBrandPopupVisible,
                          }"
                        />
                      </div>
                      <div
                        class="line"
                        v-if="isSearchBrandExitEnable"
                      ></div >
                      <div v-if="isSearchBrandExitEnable" class="exit-box-section" >
                      <img alt=""
                        class="exit-brand-icon"
                        v-if="isSearchBrandExitEnable"
                        @click="resetSearchBrand()"
                        src="@/assets/images/close-green.png"
                      />
                    </div>
                    </div>
                  </div>
                </div>
                <div v-show="selectedShoppableName !== 'Not Shoppable' && !isSelectionEnabled" class="search-box-add-product-button" :class="{'disable-content': isDataLoading}">
                  <div class="search-box-edit-product">
                    <input
                      autocomplete="off"
                      type="text"
                      class="search-input-box"
                      placeholder="Search by product id or name"
                      v-model.trim="searchProductQuery"
                      @keyup.enter="searchProductAsync()"
                      :class="{
                        'align-search-input-box': searchProductQuery,
                      }"
                    />
                    <div>
                      <img alt=""
                        class="search-icon-green-edit-product-image"
                        src="@/assets/images/search-icon-green.png"
                        @click="searchProductAsync()"
                      />
                      <img alt=""
                        class="exit-search-icon"
                        v-if="isSearchExitEnable"
                        @click="resetQuery()"
                        src="@/assets/images/exit-gray.png"
                      />
                    </div>
                  </div>
                  <div v-if="ingredientProductMatchesList.length && !isIngredientMatchLoading && !isSearchExitEnable" class="select-button text-h3">
                    <span @click="selectProducts()">{{ $t('COMMON.SELECT')  }}</span></div>
                </div>
              </div>
              <simple-sticky-wrapper
                v-if="isSelectionEnabled"
                :top="70"
                :distance="-60"
                class="edit-selection-container"
              >
                <div class="edit-selection-panel">
                  <div class="edit-select-all-checkbox-section">
                    <label class="checkbox checkbox-20 checkbox-without-text">
                      <input
                        type="checkbox"
                        :checked="selectionOfRecipes[0].isSelected"
                        @click="selectAllMatches()"
                      />
                      <span class="checkmark"></span>
                    </label>
                  </div>
                  <button
                    type="button"
                    @click="selectAllMatches()"
                    class="btn-reset text-h3"
                  >
                    {{ $t("PAGE.RECIPES.SELECT_ALL") }}
                  </button>
                  <div class="edit-selection">
                    <div class="edit-selected-text">
                      {{ checkSelectIngredient }} {{ $t("COMMON.SELECTED") }}
                      <span
                        v-if="selectedProducts.length"
                        class="edit-selected-cross-icon"
                      >
                        <img
                          src="@/assets/images/close.svg?skipsvgo=true"
                          @click="removeAllSelected()"
                          alt="edit-close-icon"
                        />
                      </span>
                    </div>
                  </div>
                  <div class="edit-btn-container">
                    <button
                      type="button"
                      class="btn-red"
                      :disabled="selectedProducts.length == 0 && selectedShoppableName !== 'Not Shoppable'"
                      @click="deleteSelect()"
                    >
                      {{ $t("BUTTONS.DELETE_BUTTON") }}
                    </button>
                    <button
                      type="button"
                      class="btn-green-text btn-small"
                      @click="cancelSelect()"
                    >
                      {{ $t("BUTTONS.CANCEL_BUTTON") }}
                    </button>
                  </div>
                </div>
              </simple-sticky-wrapper>
              <div
                v-if="isFilterBrandPopupVisible"
                class="ingredient-search-brand-main popup"
              >
                <div class="ingredient-brand-search-bar">
                  <div class="search-bar-content">
                    <img alt=""
                      @click="searchBrandAsync()"
                      class="search-icon-grey-image"
                      src="@/assets/images/search-grey.png"
                    />
                    <input
                      autocomplete="off"
                      v-model.trim="brandSearchQuery"
                      type="text"
                      class="search-bar-text text-title-2 font-normal"
                      placeholder="Search by Brand"
                      @keyup.enter="searchBrandAsync()"
                      @keyup.down="dietAutocompleteArrowDown()"
                      @keyup.up="dietAutocompleteArrowUp()"
                      :class="{
                        'align-search-input-box': brandSearchQuery,
                      }"
                    />
                    <img alt=""
                      id="ingredientSearchBrandResetQueryIcon"
                      class="exit-search-icon"
                      @click="resetSearchQuery()"
                      src="@/assets/images/exit-gray.png"
                    />
                  </div>
                </div>
                <div ref="dropdown" class="ingredient-search-brand-data-main"
                :class="(searchListData.length > 5) ? '': 'ingredient-search-main-container'" :id="`button`">
                  <div v-if="isFilterBrandLoading" class="table-image-loader">
                    <div class="loader"></div>
                  </div>
                  <div
                    ref="addTable"
                    v-if="!isFilterBrandLoading"
                    class="brand-details-checkbox"
                  >
                    <div
                      :class="
                        isSelectedAllBrandChecked
                          ? 'add-ingredients-background ingredient-brand-data'
                          : 'ingredient-brand-data'
                      "
                      @click="selectedAllBrandName()"
                      v-show="!isBrandSearchEnable"
                      ref="selectedAllBrand"
                    >
                      <div class="round">
                        <input  v-if="isSelectedAllBrandChecked"  type="radio" id="select-all-brand-radio" />
                        <label for="select-all-brand-radio" aria-label="Select All Brands" />
                      </div>
                      <div class="brand-search-list-data">
                        <div class="search-brand-sort-name text-title-2 font-normal">
                          All
                        </div>
                        <div class="search-brand-quantity text-title-2 font-normal">
                          {{allBrandQuantity > 0 ? allBrandQuantity : 0 }}
                        </div>
                      </div>
                    </div>
                  </div>
                  <div
                    ref="addTable"
                    v-if="!isFilterBrandLoading"
                    class="brand-details-checkbox"
                  >
                    <div
                      v-for="(info, index) in searchListData"
                      :key="`searchListData${index}`"
                      :class="
                        info.isChecked
                          ? 'add-ingredients-background ingredient-brand-data'
                          : 'ingredient-brand-data'
                      "
                        :id="`selected${index}`"
                        @click="selectedBrandName(info,index)"
                        ref="optionItems"
                    >
                      <div class="round">
                        <input v-if="info.isChecked" type="radio"  id="searchList"/>
                        <label for="searchList" aria-label="searchListBrands"/>
                      </div>
                      <div class="brand-search-list-data">
                        <div class="search-brand-sort-name text-title-2 font-normal">
                          {{ info[0] }}
                        </div>
                        <div class="search-brand-quantity text-title-2 font-normal">
                          {{ info[1] }}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="no-result-for-brand text-title-2" v-if="searchListData.length==0 && !isFilterBrandLoading">
                  {{ $t('COMMON.NO_RESULTS') }}
                </div>
                <div class="apply-btn-container">
                  <div class="filter-save-btn">
                    <button type="button" :class="hasEnableBrandApplyButton ? 'btn-green' : 'disabled-button btn-green'" @keydown="preventEnterAndSpaceKeyPress($event)" @click="applyFilter()">
                      {{ $t('BUTTONS.APPLY_BUTTON') }}
                    </button>
                  </div>
                </div>
              </div>
              <div class="loader" v-if="isDataLoading">
                <loader />
              </div>
              <div v-if="!isDataLoading" :class="(selectedShoppableName == 'Not Shoppable') ? 'crud-table-edit-ingredient-product disable-table ' :
              'crud-table-edit-ingredient-product'">
                <div  style="width: 100%" class="main-table-edit-ingredient-product">
                  <div v-if="!isSearchMode && !isIngredientMatchLoading" id="ing-table"  class="product-table-ingredient-head">
                    <div class="product-ingredient-title">
                      <div class="product-table-srno"></div>
                      <div class="product-table-image-container"></div>
                      <div class="product-name">PRODUCT</div>
                      <div class="product-id">PRODUCT ID</div>
                      <div class="product-button-container"></div>
                      <div class="product-menu"></div>
                    </div>
                  </div>
                  <div class="ingredient-loader-section" v-show="isIngredientMatchLoading">
                    <div class="content">
                      <div class="loading">
                        <div class="input-loading">
                          <div class="loader-image"></div>
                        </div>
                        <div class="loading-text">
                          <p>{{ $t('LOADER.LOADING') }}</p>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div
                  :id="!ingredientProductMatchesList.length?'one':''"
                    class="table-body-edit-ingredient-product"
                    v-if="!isSearchMode && !isIngredientMatchLoading"
                  >
                    <div @click="selectMatchToDelete(index,product)" :id="product.isDeleteSelected ? 'delete-selected':''" :class="!isSelectionEnabled ? 'edit-ingredient-product-table' : 'edit-ingredient-product-table hover-delete'"
                      v-for="(product, index) in ingredientProductMatchesList"
                      :key="`ingredientProductMatchesList${index}`"
                    >
                      <div v-if="isSelectionEnabled"  :class="isSelectionEnabled ?'product-table-srno-checkbox':'product-table-srno'">
                        <div id="select-all-check-box-id"  class="select-all-checkbox-section">
                          <label class="checkbox checkbox-20 checkbox-without-text" for="select-all-check-box-id" aria-label="Select All Checkbox">
                              <input @click="selectMatchToDelete(index,product)" :checked="product.isDeleteSelected"  type="checkbox" >
                              <span class="checkmark"></span>
                          </label>
                      </div>
                      </div>
                      <div v-if="!isSelectionEnabled" class="product-table-srno"></div>
                      <div class="product-table-image-container">
                        <div class="product-table-image">
                          <img alt=""
                            class="product-image"
                            :src="
                              product&&product.image&&product.image.url
                                ? product.image.url
                                : defaultImage
                            "
                          />
                        </div>
                      </div>
                      <div class="product-name text-h3">
                        {{ getProductName(product) }}
                      </div>
                      <div class="product-id">
                        {{ getProductId(product) ? getProductId(product) : "" }}
                      </div>
                      <div class="product-button-container">
                        <button type="button"
                        :id="isSelectionEnabled ?'selection-eanabled':''"
                        class="btn-green-outline"
                          @click="promoteProduct(product,index)"
                          @keydown="preventEnterAndSpaceKeyPress($event)"
                        >
                          {{ $t('COMMON.PROMOTE') }}
                        </button>
                      </div>
                      <div  class="product-menu">
                        <img  alt=""
                        :id="isSelectionEnabled ?'selection-eanabled':''"
                          @click="deletePopupVisible(product)"
                          class="delete-icon"
                          src="@/assets/images/delete-icon.png"
                        />
                      </div>
                    </div>
                    <div
                      class="zero-promoted-matches-ingredient-table"
                      v-if="!ingredientProductMatchesList.length"
                    >
                      0 Product Matches.
                      <span>
                        Products will be auto-selected based on match rating.
                      </span>
                    </div>
                  </div>
                  </div>
                  <div v-if="isSearchMode && !isIngredientMatchLoading" class="search-table-heading">
                    <div class="search-table-heading-row">
                      <div class="product-table-srno"></div>
                      <div class="product-table-image-container"></div>
                      <div class="product-name">PRODUCT</div>
                      <div class="product-id">PRODUCT ID</div>
                      <div class="product-button-container"></div>
                      <div class="product-menu"></div>
                    </div>
                  </div>
                  <div
                    v-if="isSearchMode"
                    class="search-table-body text-light-h3"
                  >
                    <div
                      class="search-table-body-row"
                      v-for="(product, index) in searchList"
                      :key="`searchList${index}`"
                    >
                    <div class="product-table-srno"></div>
                    <div class="product-table-image-container">
                      <div class="product-table-image">
                        <img alt=""
                          class="product-image"
                          :src="
                            product&&product.image&&product.image.url
                              ? product.image.url
                              : defaultImage
                          "
                        />
                      </div>
                    </div>
                    <div class="product-name">
                      {{ getProductName(product) }}
                    </div>
                    <div class="product-id">
                      {{ getProductId(product) ? getProductId(product) : "" }}
                    </div>
                    <div class="product-button-container">
                      <button type="button"
                        class="btn-green-outline"
                        v-if="!product.isAdded && !product.isPromoted"
                        @click="addProduct(product)"
                        @keydown="preventEnterAndSpaceKeyPress($event)"
                      >
                        {{ $t('COMMON.ADD') }}
                      </button>
                      <button type="button" class="btn-green-outline" v-if="product.isAdded && !product.isPromoted"
                        @click="promoteProduct(product,index)" @keydown="preventEnterAndSpaceKeyPress($event)"
                      >
                        {{ $t('COMMON.PROMOTE') }}
                      </button>
                      <button type="button" class="btn-green-outline disabled-button" v-if="product.isPromoted">
                        {{ $t('COMMON.PROMOTE') }}
                      </button>
                    </div>
                    <div class="product-menu">
                      <img
                        v-if="product.isAdded && !product.isPromoted"
                        @click="deletePopupVisible(product)"
                        class="delete-icon"
                        src="@/assets/images/delete-icon.png"
                        alt=""
                      />
                    </div>
                  </div>
                </div>
                <div v-if="isDataSearchLoading && isSearchMode && searchList.length == 0">
                  <div class="no-searched-result-found">
                    <div>
                      {{ $t('COMMON.NO_RESULTS') }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="loading" v-if="isUpdating">
              <div class="content">
                <div class="input-loading">
                  <div class="loader-image"></div>
                </div>
                <div class="loading-text">
                  <p v-if="!isDeleteSelectedText">{{ $t('UPDATING') }}...</p>
                  <p v-if="isDeleteSelectedText">Updating. Please wait...</p>
                </div>
              </div>
            </div>
          </div>
          <paginate
            id="pagination-block"
            v-if="((productTotal>sizeProduct) && ingredientProductMatchesList.length && !isSearchMode)"
            v-model="currentPage"
            :total-rows="productTotal"
            :page-range="pageRange"
            :per-page="sizeProduct"
            :page-count=Math.ceil(productTotal/sizeProduct)
            first-button-text="<<"
            prev-text="<"
            next-text=">"
            last-button-text=">>"
            :prev-class="'prev'"
            :next-class="'next'"
            :first-last-button=true
            :click-handler="pageChangeAsync"
            :container-class="'pagination'"
            :page-class="'page-item'"
            :page-link-class="'page-link'"
            :disabled-class="'disabled-pagination'"
            :active-class="'active'"
            :margin-pages="marginPages"
          >
      </paginate>
      <paginate
            id="pagination-block"
            v-if="(sizeSearchProduct<searchProductsTotal) && isSearchMode && searchList.length > 0"
            v-model="searchCurrentPage"
            :total-rows="searchProductsTotal"
            :page-range="pageRange"
            :per-page="sizeSearchProduct"
            :page-count=Math.ceil(searchProductsTotal/sizeSearchProduct)
            first-button-text="<<"
            prev-text="<"
            next-text=">"
            last-button-text=">>"
            :prev-class="'prev'"
            :next-class="'next'"
            :first-last-button=true
            :click-handler="searchPageChangeAsync"
            :container-class="'pagination'"
            :page-class="'page-item'"
            :page-link-class="'page-link'"
            :disabled-class="'disabled-pagination'"
            :active-class="'active'"
            :margin-pages="marginPages"
          >
      </paginate>
        </div>
      </div>
    </div>
    <saveModal v-if="isSaveModalVisible"
      :closeModal="closeModal"
      :saveAndPublishFunction="saveButtonClickAsync"
      :availableLang="[]"
      :buttonName="$t('BUTTONS.SAVE_BUTTON')"
      :description="$t('DESCRIPTION_POPUP.SAVE_UPDATES_POPUP')"
      :imageName="saveImage"
    />
    <Modal v-show="isPublishSuccessfully" @close="closeModal">
      <template #nutrition>
        <div class="edit-product-publish-modal">
          <div class="publish-content">
            <div class="publish-head">
              Product successfully saved and published to production!
            </div>
            <div class="button-container">
              <button type="button" class="btn-green" @click="closeModal">Okay</button>
            </div>
          </div>
        </div>
      </template>
    </Modal>
    <deleteModal
    v-if="isSelectDeleteModalVisible"
    :closeModal="closeModal"
    :productInfoTitle="'Delete Product Matches?'"
    :productDescriptionOne="'Are you sure you want to delete selected '"
    :productDescriptionTwo="'Product Matches?'"
    :deleteItem="deleteSelectProductMatchesAsync"
    :availableLanguage="0"
    :buttonText="$t('BUTTONS.DELETE_BUTTON')"
  />
    <deleteModal
      v-if="isDeleteModalVisible"
      :closeModal="closeModal"
      :productInfoTitle="'Remove Product?'"
      :productDescriptionOne="'Are you sure you want to remove this product from the'"
      :productDescriptionTwo="'ingredient?'"
      :deleteItem="deleteProduct"
      :availableLanguage="0"
      :buttonText="$t('BUTTONS.REMOVE_BUTTON')"
    />
    <cancelModal
    v-if="isConfirmModalVisible"
    :availableLang="[]"
    :isCampaignModifiedFromShoppableReview="false"
    :callConfirm="backToTag"
    :closeModal="closeModal"
    />
    <Modal v-if="isShopPreviewPopup" @close="closeModal">
      <template #shoppableReviewForEditProduct>
        <shoppableReviewOneIngredient
            :recipeIngredient="recipeIngredient"
            :recipeName="recipeName"
            :recipeSubtitle="recipeSubtitle"
            :recipeID="recipeID"
            :recipeImage="recipeImage"
            :externalImageURL="externalImageURL"
            :recipeProvider="recipeProvider"
            :ingredientCampaign="recipeIngredientCampaign"
            :ingredientNames="ingredientNames"
            :ingredientReferenceProductID="ingredientReferenceProductID"
            :globalKeywords="globalKeywords"
            :isAdminCheck="isAdminCheck"
            :checkProductTagPopup="checkProductTagPopup"
        >
        </shoppableReviewOneIngredient>
      </template>
    </Modal>
    <Modal v-if="isShowCannotUpdateRecipe" @close="closeModal">
      <template #nutrition>
        <div class="popup-for-edit-product-cannot-update-recipe">
          <div class="message">
            {{ cannotUpdateRecipeMessage }}
          </div>
          <div class="edit-product-cannot-update-recipe-ok-btn" @click="closeModal">
            OK
          </div>
        </div>
      </template>
    </Modal>
    <Modal v-show="isNameModalVisible" @close="closeModal">
        <template #nutrition>
          <div class="save-recipe-variant-modal">
            <div class="save-info-popup-container">
              <div class="save-image">
                <div class="save-image-container">
                  <img alt=""
                    class="save-icon"
                    src="~/assets/images/attention.svg?skipsvgo=true"
                  />
                </div>
              </div>
            </div>
            <div class="publish-content">
              <div class="publish-head">Are you sure you want to change the ingredient name?</div>
              <div class="note-message">Changing the name can alter promoted/matched products and shoppable flags.</div>
              <div class="button-container">
                <button type="button" class="btn-green-outline" @click="closeNameChangeModal()">{{ $t('BUTTONS.CANCEL_BUTTON') }}</button>
                <button type="button" class="btn-green" @click="editIngredientName()">{{ $t('BUTTONS.CONFIRM_BUTTON') }}</button>
              </div>
            </div>
          </div>
        </template>
      </Modal>
    <Modal v-if="hasEditIngredientPopup"  @close="closeModal">
      <template #editIngredientsName>
        <div class="edit-ingredients-name-container">
          <div v-if="hasEditIngredientLoader">
            <div class="table-image-loader">
              <div class="loader"></div>
              <div class="loader-ingredient-text">Ingredient name update in progress…</div>
            </div>
          </div>
          <div v-else>
            <div class="edit-ingredients-name-top-part">
              <div class="edit-ingredient-name-heading">Edit Ingredient name</div>
              <img alt=""
                  class="edit-ingredients-name-close-modal"
                  @click="closeModal"
                  src="~/assets/images/exit-gray.png"
              />
            </div>
            <div v-for="(editIngredient,index) in finalIngredientList" :key="`editIngredient${index}`" v-show="defaultIngredientList.length" class="input-container">
            <div class="edit-ing-headers">
             <div><span class="ing-input-heading">Ingredient name: {{editIngredient.name}}</span></div>
            </div>
             <div class="singular-name-container">
             <div class="singular-loader-container">
             <div :style="styleForTooltip" :id="`editIngloader${index}`" class="input-loading" >
                  <div class="loader-image"></div>
              </div>
             <input class="edit-ingredient-input"
                  type="text"
                  placeholder="Enter new Ingredient name"
                  @input="editSingularNameChanged(index,editIngredient.value)"
                  @keypress="preventSpecialCharacters($event)"
                  v-model="editIngredient.value"
                >
              </div>
                <div class="singular-ingredient-note-container" >
                 <span class="ing-note">Ingredient note*</span>
                 <input
                  class="edit-ingredient-note"
                  type="text"
                  placeholder="Add note (optional)"
                  v-model="editIngredient.note"
                  autocomplete="off"
                >
                </div>
             </div>
            </div>
            <div class="about-notes-cont">
              <div class="ingredient-already-exit-message" >
                <span v-show="isSingularNameExists" >This ingredient already exists. Applying your changes will replace current settings with those of the existing ingredient.</span>
              </div>
              <div class="about-notes">
                <p>*If the recipe already has an ingredient note, this note will be appended at the end with a comma.</p>
              </div>
            </div>
            <div class="edit-ingredients-name-button-container">
              <button type="button"
                  class="btn-green-outline"
                  @click="closeModal()"
                  @keydown="preventEnterAndSpaceKeyPress($event)"
              >
                {{ $t('BUTTONS.CANCEL_BUTTON') }}
              </button>
              <button type="button"
                  class="btn-green"
                  :class="applyBtn
                      ? 'btn-green'
                      : 'btn-green disabled-button'
                  "
                  @click="openNameChangeModal()"
                  @keydown="preventEnterAndSpaceKeyPress($event)"
              >
                {{ $t('BUTTONS.APPLY_BUTTON') }}
              </button>
            </div>
          </div>
        </div>
      </template>
    </Modal>
    <Modal class="modal-section-ingredient-name-list" v-show="isIngredientNameListPopupModal" @close="closeModal">
      <template #nutrition>
        <div  class="main-section-ingredient-name-list" >
        <div v-if="isIngredientNameSaving" class="edit-filter-publish-modal" @close="closeModal">
          <div class="content">
            <div class="input-loading">
              <div class="loader-image"></div>
            </div>
            <div class="publishing-text">
              <p>{{ $t('LOADER.SAVING_MESSAGE') }}</p>
            </div>
          </div>
        </div>
         <div class="sub-section-ingredient-name-list" v-if="!isIngredientNameSaving" >
          <div class="ingredient-name-list">
            <div class="Ingredient-text" >
              <span class="ingredient-name-header">Ingredient name list: </span>
              <div
                class="style-Ingredient-name-main"
                :class="{
                  'simple-data-tooltip simple-data-tooltip-bottom': ingredientPopupName
                }"
                :data-tooltip-text="ingredientPopupName && shortestIngredientName"
              >
                <p class="style-Ingredient-name"
                :id="`ingredientPopProductName`"
                @mouseover="checkIngredientPopName()"
                @mouseleave="hideIngredientNamePopTip()">{{shortestIngredientName}}</p>
              </div>
            </div>
            <div class="close-image-icon">
                <img alt=""
                  @click="cancelPopup()"
                  src="~/assets/images/exit-gray.png"
                />
              </div>
          </div>
          <div class="count-Ingredient-name" > {{ingredientNameListLength}} Ingredient<span v-if="ingredientNameListLength > 1" > names:</span><span v-else> name:</span></div>
          <div class="Ingredient-name-list-main">
            <div v-for="(data,idx) in ingredientMultipleNameList" :key="`ingredientMultipleNameList${idx}`"  class="Ingredient-name-list">{{data}}</div>
          </div>
          <div class="main-section-keyword" v-if="isAdminCheck">
            <div
            :class="
                globalKeywords.length==0
                   ? 'keyword-section'
                   : 'keyword-section-global' "

            >Global keywords:</div>
            <div class="keyword-right-section" @click="focusGlobalInput($event)">
              <div class="global-keyword-container">
                <div
                  v-for="(data,index) in globalKeywords"
                  :key="`globalKeywords${index}`"
                  class="global-keyword-row"
                  :class="{
                    'simple-data-tooltip': globalKeywordsTootlip
                  }"
                  :data-tooltip-text="globalKeywordsTootlip && data">
                  <div
                    @mouseover="showGlobalKeywordTooltip(index)"
                    @mouseleave="hideGlobalKeywordTooltip(index)"
                    :id="`GlobalKeyword${index}`"
                    class="global-keywords-name"
                  >
                    {{data}}
                  </div>
                  <div class="cross-icon">
                    <img alt="" class="remove-global-keyword-image" src="@/assets/images/close.svg?skipsvgo=true"
                    @click="removeKeyword(index)"
                    />
                  </div>
                </div>
              </div>
              <input
                autocomplete="off"
                type="text"
                :class="
                globalKeywords.length==0
                   ? 'placeholder-keywords'
                   : 'placeholder-keywords-enter'"
                :placeholder="
                globalKeywords.length==0
                   ? 'List keywords separated by a comma (optional)'
                   : 'Enter keyword(s)' "
                v-model.trim="inputKeyword"
                id="globalInput"
                @keyup.enter="addGlobalKeyword(inputKeyword)"
                @blur="addGlobalKeyword(inputKeyword)"
              />
            </div>
          </div>
        <div class="hr-main-container">
          <hr class="hr-line" v-if="isAdminCheck">
          </div>
          <div class="main-section-button" v-if="isAdminCheck">
            <button type="button"
              class="btn-green-outline"
              @click="cancelPopup()"
              @keydown="preventEnterAndSpaceKeyPress($event)"
            >
              {{ $t('BUTTONS.CANCEL_BUTTON') }}
            </button>
            <button
              type="button"
              :class="['btn-green', { 'disabled-button': !isGlobalKeywordModified }]"
              @click="viewAllApplyGlobalKeywordAsync()"
              @keydown="preventEnterAndSpaceKeyPress($event)"
            >
              {{ $t('BUTTONS.SAVE_BUTTON') }}
            </button>
          </div>
          <div class="main-section-button" v-if="!isAdminCheck">
            <button type="button"
              class="btn-green"
              @click="cancelPopup()"
              @keydown="preventEnterAndSpaceKeyPress($event)"
            >
              {{ $t('BUTTONS.CANCEL_BUTTON') }}
            </button>
          </div>
         </div>
        </div>
      </template>
    </Modal>
    <savingModal
      v-show="isIngredientSaving || isIngredientSaving"
      :status="'saving'"
    />
  </content-wrapper>
</client-only>
</template>
<script setup>
import { ref, onMounted, reactive, watch, getCurrentInstance, nextTick } from "vue";
import savingModal from "@/components/saving-modal";
import cancelModal from "@/components/cancel-modal";
import saveModal from "@/components/save-modal";
import Modal from "@/components/Modal";
import deleteModal from "@/components/delete-modal";
import shoppableReviewOneIngredient from "@/components/shoppableReviewOneIngredient";
import ContentWrapper from "@/components/content-wrapper/content-wrapper.vue";
import loader from "@/components/loader.vue";

// composables
import { useRefUtils } from "@/composables/useRefUtils";
import { useCommonUtils } from "@/composables/useCommonUtils";
import { useDelayTimer } from "@/composables/useDelayTimer";
import { useEventUtils } from "@/composables/useEventUtils";
import { useProjectLang } from "@/composables/useProjectLang";

// images
import defaultImage from "~/assets/images/default_recipe_image.png";
import saveImage from "~/assets/images/1014367-MQuADjfW4ulIQ-en-US-0.png";

// utility
import { useStore } from "vuex";
import { useNuxtApp } from "#app";
import { useI18n } from "vue-i18n";
import { useRouter, useRoute } from "vue-router";
import { useAuth0 } from '@auth0/auth0-vue';
import { QUERY_PARAM_KEY } from "../сonstants/query-param-key.js";
import SimpleStickyWrapper from "@/components/simple-sticky-wrapper.vue";

// utility functions declare
const { readyProject, isAdmin } = useProjectLang();
const store = useStore();
const { triggerLoading, parseInputString } = useCommonUtils();
const { $eventBus } = useNuxtApp();
const { getRef } = useRefUtils();
const { preventSpecialCharacters, preventEnterAndSpaceKeyPress } = useEventUtils();
const { delay } = useDelayTimer();
const instance = getCurrentInstance();
const $keys = instance.appContext.config.globalProperties.$keys;
const { t } = useI18n();
const route = useRoute();
const router = useRouter();
const { user } = useAuth0();

// states
const titleRefs = ref([]);
const isTitleTruncated = ref([]);
const isDeleteSelectedText = ref(false);
const isDataLoading = ref(false);
const isSelectDeleteModalVisible = ref(false);
const isNameModalVisible = ref(false);
const isIngredientSaving = ref(false);
const isPageLoading = ref(true);
const isPopupSaved = ref(false);
const isIngredientMatchLoading = ref(false);
const isIngredientPromoteLoading = ref(false);
const isConfirmModalVisible = ref(false);
const isShopPreviewPopup = ref(false);
const isFilterBrandPopupVisible = ref(false);
const isSaveModalVisible = ref(false);
const isDeleteModalVisible = ref(false);
const isSearchMode = ref(false);
const isDisplayShoppableReview = ref(false);
const isIngredientNameListPopupModal = ref(false);
const hasEditIngredientPopup = ref(false);
const hasEditIngredientLoader = ref(false);
const hasLockIngredient = ref(false);
const hasEditIngredientName = ref(false);

// Product and selection states
const selectedProducts = ref([]);
const selectionOfRecipes = ref([{ isSelected: false }]);
const selectedCount = ref(0);
const isSelectionEnabled = ref(false);
const isProductTag = ref(false);
const tempKeyword = ref([]);
const applyBtn = ref(false);
const copySearch = ref("");
const checkSearch = ref(false);
const deleteProductData = ref(null);

// Recipe data
const recipeData = ref({});
const recipeIngredientCampaign = ref([]);
const recipeIngredientIndex = ref(0);
const recipeIngredient = ref(null);
const recipeID = ref("");
const recipeProvider = ref("");
const recipeName = ref("");
const recipeSubtitle = ref("");
const recipeIngredients = ref([]);
const recipeImage = ref("");
const externalImageURL = ref("");
const ingredientNames = ref([]);
const recipeDetailsList = ref([]);
const ingredientReferenceProductID = ref("");
const recipeEmptyCount = ref(0);
const ingredientPopupName = ref(false);
const globalKeywordsTootlip = ref(false);
// Ingredient and campaign variables
const campaignIdentifier = ref("");
const campaignVersion = ref("");
const filteredGTINS = ref([]);
const promotedGTINS = ref([]);
const includedGTINS = ref([]);
const isOnlyIncluded = ref(false);
const totalPromoted = ref(0);
const isCampaignModified = ref(false);
const dropdownItem = ref([]);

// Recipe pagination
const recipePage = ref(0);
const recipePageSize = ref(10);
const totalRecipes = ref(0);
const currentPage = ref(1);
const marginPages = ref(0);
const pageRange = ref(6);

// Shoppable data
const shoppableDropdownResult = ref(false);
const selectedShoppableName = ref("");
const shoppableDataList = ref([
  { data: "Shoppable (not pantry)" },
  { data: "Shoppable (pantry)" },
  { data: "Not Shoppable" },
]);

// Brand and product search variables
const brandSearchQuery = ref("");
const selectedBrand = ref("");
const displayBrandName = ref("");
const isSearchBrandExitEnable = ref(false);
const isFilterBrandLoading = ref(false);
const allBrandQuantity = ref(0);
const searchProductQuery = ref("");
const fromSearchProduct = ref(0);
const sizeSearchProduct = ref(15);
const searchProductsTotal = ref(0);
const isSearchExitEnable = ref(false);
const fromProduct = ref(0);
const sizeProduct = ref(15);
const productTotal = ref(0);

// Other states
const showLoader = ref(false);
const isPublishSuccessfully = ref(false);
const searchList = ref([]);
const searchListData = ref([]);
const promotedIngredientMatchData = ref([]);
const ingredientsUomList = ref([]);
const ingredientProductMatchesList = ref([]);
const searchCurrentPage = ref(1);
const isProductAdded = ref(false);
const isProductPromoted = ref(false);
const countBrand = ref(0);
const isShowCannotUpdateRecipe = ref(false);
const cannotUpdateRecipeMessage = ref("");
const recipeRefreshTimer = ref(null);
const isIngredientNotFoundError = ref(false);
const ingredientNotFoundMessage = ref("");
const isDataSearchLoading = ref(false);
const styleForTooltip = reactive({ visibility: "hidden" });
const showViewAll = ref(false);
const shortestIngredientName = ref("");
const ingredientMultipleNameList = ref([]);
const isUpdating = ref(false);
const ingredientNameListLength = ref("");
const defaultIngredientList = ref([]);
const finalIngredientList = ref([]);
const tempingIndex = ref("");
const temporaryIngredient = ref("");
const isBrandSearchEnable = ref(false);
const globalKeywords = ref([]);
const inputKeyword = ref("");
const isGlobalKeywordModified = ref(false);
const copyKeywords = ref([]);
const hasDraggableTooltipDisplay = ref(false);
const isDrag = ref(false);
const hasEnableBrandApplyButton = ref(false);
const isCustom = ref(false);
const isAdminCheck = ref(false);
const isIngredientNameSaving = ref(false);
const isShowOnlyPromotedProducts = ref(false);
const lang = ref("");
const singularName = ref("");
const isSelectedAllBrandChecked = ref(false);
const hasDisableAllBrandCheckbox = ref(false);
const isSingularNameExists = ref(false);
const hasEditSingularNameChecked = ref(false);
const dietAutocompleteArrowCounter = ref(0);
const lockedCampaignTooltip = 'This ingredient has a locked campaign. It will not be automatically updated with new products.';
const unlockedCampaignTooltip = 'This is an unlocked ingredient.';
const recipeOverwriteTooltip = 'This recipe has an overwrite / override. Any changes to this form will not apply to this recipe.';
// lifecycle hooks
onMounted(async () => {
  readyProject(async ({ isProjectReady }) => {
    if (!isProjectReady) return;
    lang.value = store.getters["userData/getDefaultLang"];
    isPageLoading.value = true;
    triggerLoading($keys.KEY_NAMES.ROUTE_LOADING, isPageLoading.value);
    isAdminCheck.value = await isAdmin.value;
    titleRefs.value = new Array(recipeDetailsList.length).fill(null);
    nextTick(() => {
      checkAllTitleOverflows();
    });
    if (route.query.names) {
      singularName.value = Array.isArray(route.query.names)
        ? route.query.names[0]
        : route.query.names;

      setRecipePageSize();

      await getRecipeUnitConfigAsync();
      await getRecipesAsync();
      await getIngredientsCampaign(false);
      await checkForIngredientsAsync();
      await getBrandDistributionAsync();
      await pageChangeAsync(currentPage);
      await getFeatureConfigAsync();

      recipeRefreshTimer.value = setInterval(async () => {
        await refreshRecipeStatesAsync();
      }, 10000);

      document.addEventListener("click", handleClickOutside);
      document.addEventListener("keyup", handleESCClickOutside);
      document.addEventListener("scroll", checkScrollPosition);
      document.addEventListener("click", handleClickOutsideBrandFilterPopup);

      $eventBus.on("closeShoppableReview", closeShoppableReviewHandler);
      $eventBus.on("saveShoppableReview", saveShoppableReviewHandler);
    }
  });
});

onBeforeUnmount(() => {
  clearTimeout(recipeRefreshTimer.value);
  document.removeEventListener("keyup", handleESCClickOutside);
  document.removeEventListener("click", handleClickOutside);
  document.removeEventListener("scroll", checkScrollPosition);
  document.removeEventListener("click", handleClickOutsideBrandFilterPopup);
  $eventBus.off("closeShoppableReview", closeShoppableReviewHandler);
  $eventBus.off("saveShoppableReview", saveShoppableReviewHandler);
});

const saveShoppableReviewHandler = async (ingredient) => {
  recipeIngredient.value = ingredient;
  recipeIngredients.value.splice(recipeIngredientIndex.value, 1, ingredient);
  isIngredientSaving.value = true;
  await saveRecipeCampaigns();
  await delay(1000);
  await getRecipesAsync();
  triggerLoading($keys.KEY_NAMES.SAVED_SUCCESS);
  isIngredientSaving.value = false;
  isShopPreviewPopup.value = false;
  isPopupSaved.value = true;
  await delay(3000);
  isPopupSaved.value = false;
};

const closeShoppableReviewHandler = async () => {
  isShopPreviewPopup.value = false;
};

// Check scroll position
const checkScrollPosition = () => {
  if (isSelectionEnabled.value) {
    const ele = document.querySelector("#ing-table");
    if (ele && ele.getBoundingClientRect().top < 0 && ingredientProductMatchesList.value.length >= 4) {
      changeSelectBarPosition();
    }
  }
};
const setTitleRef = (el, index) => {
  if (el) {
    titleRefs.value[index] = el;
  }
};
const checkAllTitleOverflows = () => {
  isTitleTruncated.value = titleRefs.value.map((refEl, i) => {
    if (!refEl) return false;
    const p = refEl.querySelector('.recipe-title-text');
    if (!p) return false;
    const diff = p.scrollHeight - p.clientHeight;
    const isOverflowing = diff > 2;
    return isOverflowing;
  });
};

const changeSelectBarPosition = () => {
  const ele = document.querySelector(".selection-container");
  const deleteBtn = document.querySelector(".btn-container");
  const cancelBtn = document.querySelector(".cancel-btn");
  const selectText = document.querySelector(".selected-text");
  const selectAll = document.querySelector(".select-all-text");
  const selectBox = getRef("select-all-check-box-id");

  if (isSelectionEnabled.value) {
    if (ele) {
      ele.style.backgroundColor = "#FFFFFF";
      ele.style.height = "64px";
      ele.style.alignItems = "center";
      ele.style.paddingTop = "inherit";
      ele.style.position = "fixed";
      ele.style.zIndex = "999";
      ele.style.top = "60px";
      ele.style.width = "100%";
      ele.style.marginLeft = "-20px";
      ele.style.boxShadow = "1px 1px 4px 0px #888888";
    }
    if (deleteBtn) {
      deleteBtn.style.right = "486px";
    }
    if (cancelBtn) {
      cancelBtn.style.right = "330px";
    }
    if (selectText) {
      selectText.style.left = "161px";
    }
    if (selectAll) {
      selectAll.style.left = "74px";
    }
    if (selectBox) {
      selectBox.style.marginLeft = "29px";
    }
  }
};

// Async function to delete selected product matches
const deleteSelectProductMatchesAsync = async () => {
  isUpdating.value = true;
  isDeleteSelectedText.value = true;
  isCampaignModified.value = true;
  isSelectDeleteModalVisible.value = false;

  if (selectedProducts.value.length > 0) {
    selectedProducts.value.forEach((item) => {
      if (includedGTINS.value.includes(item.gtin)) {
        const idx = includedGTINS.value.indexOf(item.gtin);
        includedGTINS.value.splice(idx, 1);
      } else if (!filteredGTINS.value.includes(item.gtin)) {
        filteredGTINS.value.push(item.gtin);
      }
    });

    const deletedCount = selectedProducts.value.length;
    productTotal.value = Math.max(0, productTotal.value - deletedCount);
    const lastPageAfterDeletion = Math.max(
      1,
      Math.ceil(productTotal.value / sizeProduct.value)
    );
    if (currentPage.value > lastPageAfterDeletion) {
      currentPage.value = lastPageAfterDeletion;
      await pageChangeAsync(currentPage.value);
    }
  }

  if (ingredientProductMatchesList.value.length <= 1 && currentPage.value > 1) {
    fromProduct.value = fromProduct.value - sizeProduct.value;
    currentPage.value = currentPage.value - 1;
  }

  if (isSearchMode.value && !isSelectionEnabled.value) {
    const product = deleteProductData.value;
    product.isAdded = false;
  } else {
    await getIngredientProductMatchesAsync();
  }

  if (productTotal.value === 0) {
    resetSearchBrand();
  }

  triggerLoading($keys.KEY_NAMES.DELETED);
  selectedProducts.value = [];
  selectionOfRecipes.value[0].isSelected = false;
  isSelectionEnabled.value = false;
  isDeleteSelectedText.value = false;
};

// Remove all selected items
const removeAllSelected = () => {
  ingredientProductMatchesList.value.forEach((item) => {
    item.isDeleteSelected = false;
  });
  selectionOfRecipes.value[0].isSelected = false;
  selectedProducts.value = [];
};
const selectAllMatches = () => {
  selectionOfRecipes.value[0].isSelected =
    !selectionOfRecipes.value[0].isSelected;

  if (selectionOfRecipes.value[0].isSelected) {
    ingredientProductMatchesList.value.forEach((item) => {
      item.isDeleteSelected = true;
      selectedProducts.value.push(item);
    });
  } else {
    ingredientProductMatchesList.value.forEach((item) => {
      item.isDeleteSelected = false;
      selectedProducts.value = selectedProducts.value.filter(
        (insideItem) => insideItem.externalId !== item.externalId
      );
    });
  }

  selectedProducts.value = selectedProducts.value.filter(
    (value, index, self) =>
      index === self.findIndex((t) => t.externalId === value.externalId)
  );
};

// Check selected items
const checkSelected = () => {
  const count = ingredientProductMatchesList.value.filter(
    (item) => item.isDeleteSelected
  ).length;

  selectionOfRecipes.value[0].isSelected =
    count === ingredientProductMatchesList.value.length;
};

// Select match to delete
const selectMatchToDelete = (data, product) => {
  if (isSelectionEnabled.value) {
    ingredientProductMatchesList.value.forEach((item, index) => {
      if (index === data) {
        item.isDeleteSelected = !item.isDeleteSelected;

        if (item.isDeleteSelected) {
          selectedProducts.value.push(item);
        } else {
          selectedProducts.value = selectedProducts.value.filter(
            (insideData) => insideData.externalId !== product.externalId
          );
        }
      }
    });
    checkSelected();
  }
};

// Trigger delete confirmation modal
const deleteSelect = () => {
  if (selectedProducts.value.length > 0) {
    isSelectDeleteModalVisible.value = true;
  }
};

// Cancel selection
const cancelSelect = () => {
  isSelectionEnabled.value = false;
  selectedProducts.value = [];

  if (ingredientProductMatchesList.value.length > 0) {
    selectionOfRecipes.value[0].isSelected = false;
    ingredientProductMatchesList.value.forEach((item) => {
      item.isDeleteSelected = false;
    });
  }
};
const selectProducts = () => {
  isSelectionEnabled.value = true;
};

// Back to tag
const backToTag = () => {
  backToIngredientsConfirm();
  closeModal();
};

// Get feature config asynchronously
const getFeatureConfigAsync = async () => {
  const params = { lang: lang.value };
  try {
    await store.dispatch("ingredient/getFeatureConfigAsync", { params });
    const response = store.getters["ingredient/getFeatureConfig"];
    isDisplayShoppableReview.value = response?.shoppableReview ?? false;
  } catch (e) {
    console.error(e);
  }
};

// Check product tag popup
const checkProductTagPopup = (data) => {
  isProductTag.value = !!data;
};

// Handle ESC click outside
const handleESCClickOutside = (event) => {
  if (event?.key === "Escape") {
    closeModalonEsc();
  }
};

// Show global keyword tooltip
const showGlobalKeywordTooltip = (index) => {
  const element = getRef(`GlobalKeyword${index}`);
  if (element && element.scrollWidth > element.clientWidth) {
    globalKeywordsTootlip.value = true;
  }
};

// Hide global keyword tooltip
const hideGlobalKeywordTooltip = (index) => {
  const element = getRef(`GlobalKeyword${index}`);
  if (element && element.scrollWidth > element.clientWidth) {
    globalKeywordsTootlip.value = false;
  }
};

// Focus global input
const focusGlobalInput = (event) => {
  const focusElement = event.target._prevClass === "global-keywords-name";
  const element = getRef("globalInput");
  if (element !== document.activeElement && !focusElement) {
    element.focus();
  }
};

// View all apply global keyword asynchronously
const viewAllApplyGlobalKeywordAsync = async () => {
  hasEditIngredientName.value = true;
  isIngredientPromoteLoading.value = false;
  isUpdating.value = true;
  tempKeyword.value = [];
  isGlobalKeywordModified.value = false;
  isIngredientNameSaving.value = false;

  await resetQuery();
  isIngredientNameSaving.value = true;
  await getIngredientProductMatchesAsync();
  await getBrandDistributionAsync();
  isCampaignModified.value = true;
  isUpdating.value = false;
  removeDuplicates(globalKeywords.value, copyKeywords.value);
  isIngredientNameSaving.value = false;
  isIngredientNameListPopupModal.value = false;
};

// Remove keyword
const removeKeyword = (index) => {
  globalKeywords.value = globalKeywords.value.filter(
    (_, loopIndex) => loopIndex !== index
  );
  isGlobalKeywordModified.value = true;
};

// Add global keyword
const addGlobalKeyword = (value) => {
  const keywords = parseInputString(value);
  if (keywords?.length) {
    globalKeywords.value.push(...keywords);
    inputKeyword.value = "";
    isGlobalKeywordModified.value = true;
  }
};

// Post keyword data
const postKeywordData = async () => {
  const payload = {
    ingredient: shortestIngredientName.value,
    keywords: globalKeywords.value,
  };
  await store.dispatch("ingredient/postIngredientsKeywordAsync", { payload });
  closeModal();
};

// Check for ingredients asynchronously
const checkForIngredientsAsync = async () => {
  const params = {
    ingredients: ingredientNames.value.map((name) => `${name}`),
  };
  try {
    await store.dispatch("ingredient/getIngredientKeywordsAsync", { params });
    const response = store.getters["ingredient/getIngredientKeywords"];
    if (Object.keys(response).length) {
      copyKeywords.value = [];
      const results = Object.values(response.results || {});
      results?.forEach((result) => {
        const keywords = result.keywords || [];
        keywords?.forEach((keyword) => {
          if (keyword) {
            globalKeywords.value.push(keyword);
            copyKeywords.value.push(keyword);
          }
        });
      });
      removeDuplicates(globalKeywords.value, copyKeywords.value);
    }
  } catch (e) {
    console.error(e);
  }
};

// Remove duplicates
const removeDuplicates = (arr, arrCopied) => {
  globalKeywords.value = [...new Set(arr)];
  copyKeywords.value = [...new Set(arrCopied)];
};
const closeNameChangeModal = () => {
  isNameModalVisible.value = false;
  hasEditIngredientPopup.value = true;
};

const openNameChangeModal = () => {
  isNameModalVisible.value = true;
  hasEditIngredientPopup.value = false;
};

const checkIngredientPopName = () => {
  const name = getRef("ingredientPopProductName");
  if (name.scrollWidth > name.clientWidth) {
    ingredientPopupName.value = true;
  }
};

const hideIngredientNamePopTip = () => {
  const name = getRef("ingredientPopProductName");
  if (name.scrollWidth > name.clientWidth) {
    ingredientPopupName.value = false;
  }
};

const resetBackgroundColors = () => {
  searchListData.forEach((data, index) => {
    const element = getRef("selected" + index);
    if (
      element?.style &&
      data[0].toLowerCase() !== selectedBrand.toLowerCase()
    ) {
      element.style.backgroundColor = "white";
    }
  });
  if (!isSelectedAllBrandChecked.value && dietAutocompleteArrowCounter.value > -1) {
    const selectedAllElement = getRef("selectedAllBrand");
    if (selectedAllElement?.style) {
      selectedAllElement.style.backgroundColor = "white";
    }
  }
};

const highlightCurrentSelection = () => {
  const ele = getRef("selected" + dietAutocompleteArrowCounter.value);
  if (ele) {
    ele.style.backgroundColor = "#e9fde5";
  }
  scrollToSelected();
};

const dietAutocompleteArrowDown = () => {
  resetBackgroundColors();
  if (dietAutocompleteArrowCounter.value < searchListData.length - 1) {
    dietAutocompleteArrowCounter.value += 1;
  }
  highlightCurrentSelection();
};

const dietAutocompleteArrowUp = () => {
  resetBackgroundColors();
  if (dietAutocompleteArrowCounter.value > 0) {
    dietAutocompleteArrowCounter.value -= 1;
  }
  highlightCurrentSelection();
};

const scrollToSelected = () => {
  nextTick(() => {
    const optionItems = getRef("optionItems");
    if (optionItems && optionItems.length > 0) {
      const selectedItem = optionItems[dietAutocompleteArrowCounter.value];
      if (selectedItem) {
        const dropdown = getRef("dropdown");
        const dropdownRect = dropdown.getBoundingClientRect();
        const selectedItemRect = selectedItem.getBoundingClientRect();
        if (
          selectedItemRect.top < dropdownRect.top ||
          selectedItemRect.bottom > dropdownRect.bottom
        ) {
          const scrollTop = selectedItem.offsetTop - dropdown.offsetTop;
          dropdown.scrollTop = scrollTop - 44;
        }
      }
    }
  });
};

const openEditIngredientPopup = () => {
  applyBtn.value = false;
  isSingularNameExists.value = false;
  finalIngredientList.value.forEach((item) => {
    item.value = "";
    item.note = "";
  });
  hasEditIngredientPopup.value = true;
};

const lockIngredientToggleAsync = async () => {
  hasLockIngredient.value = true;
  isPageLoading.value = true;
  isOnlyIncluded.value = !isOnlyIncluded.value;
  if (isOnlyIncluded.value) {
    await getIngredientLockedCampaignAsync();
  } else {
    await getIngredientUnlockedCampaignAsync();
  }
  currentPage.value = 1;
  triggerLoading($keys.KEY_NAMES.ROUTE_LOADING, isPageLoading.value);
  isCampaignModified.value = true;
  await getBrandDistributionAsync();
  await pageChangeAsync(currentPage.value);
};

const getIngredientLockedCampaignAsync = async () => {
  const payload = {
    identifier: campaignIdentifier.value || "",
    ingredient: singularName.value || "",
    keywords: globalKeywords.value || [],
    promotedProducts: promotedGTINS.value || [],
    includedProducts: isOnlyIncluded.value ? includedGTINS.value : [],
    filteredProducts: filteredGTINS.value || [],
    onlyPromoted: isShowOnlyPromotedProducts.value,
    onlyIncluded: isOnlyIncluded.value,
    shoppableFlag: getShoppableKey(),
  };
  try {
    await store.dispatch("ingredient/getIngredientLockedCampaignAsync", {
      payload,
    });
    const response = store.getters["ingredient/getLockedIngredient"];
    if (Object.keys(response).length) {
      const {
        includedProducts = [],
        filteredProducts = [],
        promotedProducts = [],
        onlyIncluded = false,
      } = response;
      includedGTINS.value = includedProducts;
      filteredGTINS.value = filteredProducts;
      promotedGTINS.value = promotedProducts;
      isOnlyIncluded.value = onlyIncluded;
      getIngredientProductMatchesAsync();
    }
  } catch (e) {
    console.error(e);
  } finally {
    isPageLoading.value = false;
    triggerLoading($keys.KEY_NAMES.ROUTE_LOADING, isPageLoading.value);
  }
};

const getIngredientUnlockedCampaignAsync = async () => {
  const payload = {
    identifier: campaignIdentifier.value || "",
    ingredient: singularName.value || "",
    promotedProducts: promotedGTINS.value || [],
    includedProducts: isOnlyIncluded.value || [],
    filteredProducts: filteredGTINS.value || [],
    onlyPromoted: isShowOnlyPromotedProducts.value,
    onlyIncluded: isOnlyIncluded.value,
    shoppableFlag: getShoppableKey(),
  };
  try {
    await store.dispatch("ingredient/getIngredientUnlockedCampaignAsync", {
      payload,
    });
    const response = store.getters["ingredient/getUnLockedIngredient"];
    if (Object.keys(response).length) {
      includedGTINS.value = response.includedProducts || [];
      filteredGTINS.value = response.filteredProducts || [];
      promotedGTINS.value = response.promotedProducts || [];
      isOnlyIncluded.value = response.onlyIncluded || false;
      getIngredientProductMatchesAsync();
    }
  } catch (e) {
    console.error(e);
  } finally {
    isPageLoading.value = false;
    triggerLoading($keys.KEY_NAMES.ROUTE_LOADING, isPageLoading.value);
  }
};

const ingredientNameListPopupAsync = async () => {
  tempKeyword.value = [...globalKeywords.value];
  isIngredientNameListPopupModal.value = true;
  isGlobalKeywordModified.value = false;
};

const setRecipePageSize = () => {
  if (window.innerWidth > 1950) {
    recipePageSize.value = 10;
  } else if (window.innerWidth > 1625) {
    recipePageSize.value = 8;
  } else if (window.innerWidth > 1550) {
    recipePageSize.value = 6;
  } else {
    recipePageSize.value = 5;
  }
};

const getRecipeUnitConfigAsync = async () => {
  const params = {
    lang: lang.value,
  };
  try {
    await store.dispatch("ingredient/getRecipeUnitConfigAsync", { params });
    const response = store.getters["ingredient/getRecipeUnit"];
    if (response?.units?.length) {
      ingredientsUomList.value = response.units.sort((a, b) => {
        return a?.display?.localeCompare(b?.display);
      });
    }
  } catch (e) {
    console.error(e);
  }
};

const getRecipeAsync = async (id) => {
  if (!isDisplayShoppableReview.value) return;
  const params = {
    country: lang.value.split("-")[1],
  };
  try {
    await store.dispatch("ingredient/getRecipeAsync", { params, isin: id });
    const response = store.getters["ingredient/getRecipeList"];
    return await getEditRecipeDataAsync(response);
  } catch {
    showLoader.value = false;
  }
};

const getEditRecipeDataAsync = async (recipeDataReceived) => {
  recipeData.value = recipeDataReceived;
  recipeID.value = recipeData.value.isin || "";
  recipeProvider.value = recipeData.value.provider || "";
  recipeName.value = recipeData.value?.title?.[lang.value] ?? "";
  recipeSubtitle.value = recipeData.value?.subtitle?.[lang.value] ?? "";
  recipeImage.value = recipeData.value?.media?.[lang.value]?.image ?? "";
  externalImageURL.value =
    recipeData.value?.media?.[lang.value]?.externalImageUrl ?? "";

  const recipeIngredients = recipeData.value.ingredients[lang.value] || [];
  await getUpdatedRecipeIngredientsDataAsync(recipeIngredients);

  if (recipeIngredientIndex.value == -1) {
    isShowCannotUpdateRecipe.value = true;
    cannotUpdateRecipeMessage.value = `The ingredient '${singularName.value}' was not found in the recipe.`;
  } else if (recipeData.value.state == "publishing") {
    isShowCannotUpdateRecipe.value = true;
    cannotUpdateRecipeMessage.value =
      "Cannot edit the recipe while it is updating. Please wait for the recipe to finish updating.";
  } else {
    recipeIngredientCampaign.value = {
      promotedProducts: promotedGTINS.value,
      includedProducts: includedGTINS.value,
      filteredProducts: filteredGTINS.value,
      onlyPromoted: isShowOnlyPromotedProducts.value,
      onlyIncluded: isOnlyIncluded.value,
      shoppableFlag: getShoppableKey(),
    };
    ingredientNames.value = ingredientNames.value ? ingredientNames.value : [];
    isShopPreviewPopup.value = true;
  }
};

const getUpdatedRecipeIngredientsDataAsync = async (data) => {
  recipeIngredients.value = [];
  const ingredientsIsinList = [];
  data.forEach((ingredientsData) => {
    const ingredient = createIngredient(ingredientsData);
    ingredient.UOM = getUomDisplay(ingredient.UOM);
    recipeIngredients.value.push(ingredient);
    if (ingredient?.foodItem) {
      ingredientsIsinList.push(ingredient.foodItem);
    }
  });

  if (ingredientsIsinList?.length && recipeProvider.value === "fims") {
    await processFoodItemsAsync(ingredientsIsinList);
  }
  updateRecipeIngredient();
};

const createIngredient = (ingredientsData = {}) => {
  const {
    name = "",
    amount = {},
    productId = "",
    rawText = "",
    foodItem = "",
    group = "",
    excludeFromNutrition = false,
    level = "main",
    modifier = "",
    note = "",
    keywords = [],
    keywordInput = "",
  } = ingredientsData;

  const {
    value: quantity = 0,
    unit: UOM = "",
    weightInGrams = "0",
    volumeInMl = "0",
  } = amount || {};

  return {
    name,
    quantity,
    UOM,
    weightInGrams: weightInGrams.toString(),
    volumeInMl: volumeInMl.toString(),
    productId,
    rawText,
    foodItem,
    uomAutocomplete: false,
    group,
    excludeFromNutrition,
    level,
    modifier,
    note,
    keywords,
    keywordInput,
  };
};
const getUomDisplay = (uom) => {
  const foundElement = ingredientsUomList.value.find(
    (element) => uom === element.key
  );
  return foundElement ? foundElement.display : "";
};

const processFoodItemsAsync = async (ingredientsIsinList) => {
  await foodItemName(ingredientsIsinList);
};

const updateRecipeIngredient = () => {
  recipeIngredientIndex.value = recipeIngredients.value.findIndex(
    (ingredient) => {
      const ingredientName = ingredient?.name?.toLowerCase().trim();
      return ingredientNames.value.includes(ingredientName);
    }
  );

  if (recipeIngredientIndex.value > -1) {
    recipeIngredient.value =
      recipeIngredients.value[recipeIngredientIndex.value];
  }
};

const foodItemName = async (dataArray) => {
  try {
    const response  = await store.dispatch("ingredient/getAllFoodItemIsinAsync", { lang: lang.value, isins: dataArray });
    const dataOfName = Object.values(response.results);

    recipeIngredients.value.forEach((item) => {
      dataOfName.forEach((namedata) => {
        if (namedata.isin === item.foodItem) {
          item.name = namedata?.name?.singular || "";
        }
      });
    });
  } catch (error) {
    console.error("Error fetching food item names:", error);
  }
};

const searchProductAsync = async () => {
  if (searchProductQuery.value !== "") {
    isIngredientMatchLoading.value = true;
    checkSearch.value = true;
    fromSearchProduct.value = 0;
    searchCurrentPage.value = 1;
    await updatePageUrlAsync(searchCurrentPage.value);

    if (searchProductQuery.value) {
      isSearchExitEnable.value = true;
      isSearchMode.value = true;
      resetSearchBrandFields();
      await searchPageChangeAsync(searchCurrentPage.value);
    } else {
      isSearchExitEnable.value = false;
      isSearchMode.value = false;
      searchList.value = [];
    }
  }
  dietAutocompleteArrowCounter.value = 0;
  scrollToSelected();
};
const selectShoppableValue = (name) => {
  if (selectedShoppableName.value === name) {
    return;
  }
  isCampaignModified.value = true;
  selectedShoppableName.value = name;
  shoppableDropdownResult.value = false;

  if (selectedShoppableName.value === "Not Shoppable") {
    cancelSelect();
    isSelectionEnabled.value = false;
  }
};

const showShoppableData = () => {
  shoppableDropdownResult.value = !shoppableDropdownResult.value;
};

const resetQuery = () => {
  isIngredientMatchLoading.value = true;
  checkSearch.value = false;
  searchProductQuery.value = "";
  isSearchMode.value = false;
  searchList.value = [];
  isProductAdded.value = false;
  isProductPromoted.value = false;
  ingredientProductMatchesList.value = [];
  currentPage.value = 1;
  fromSearchProduct.value = 0;
  pageChangeAsync(currentPage.value);
  getIngredientProductMatchesAsync();
  isSearchExitEnable.value = false;
};
const resetIngredientsCampaign = () => {
  isIngredientPromoteLoading.value = false;
  campaignIdentifier.value = "";
  campaignVersion.value = "";
  promotedGTINS.value = [];
  filteredGTINS.value = [];
  includedGTINS.value = [];
  promotedIngredientMatchData.value = [];
  totalPromoted.value = 0;
  isShowOnlyPromotedProducts.value = false;
  isOnlyIncluded.value = false;
  selectedShoppableName.value = "Shoppable (not pantry)";
};

const unPromoteProduct = (product, index) => {
  if (dropdownItem.value && dropdownItem.value.dropDown) {
    dropdownItem.value.dropDown = false;
  }
  isCampaignModified.value = true;
  product.dropDown = false;
  product.isPromoted = false;
  isUpdating.value = true;
  triggerLoading($keys.KEY_NAMES.PRODUCT_UNPROMOTED);
  promotedIngredientMatchData.value.splice(index, 1);

  const gtinIndex = promotedGTINS.value.indexOf(product.gtin);
  if (gtinIndex !== -1) {
    promotedGTINS.value.splice(gtinIndex, 1);
  }

  if (isSearchExitEnable.value) {
    resetQuery();
  }

  totalPromoted.value = promotedGTINS.value.length;

  if (!isSearchMode.value) {
    getBrandDistributionAsync();
    resetSearchBrand();
  }

  if (promotedIngredientMatchData.value.length === 0) {
    isShowOnlyPromotedProducts.value = false;
  }
};

const promoteProduct = (product, index) => {
  if (dropdownItem.value && dropdownItem.value.dropDown) {
    dropdownItem.value.dropDown = false;
  }
  isFilterBrandPopupVisible.value = false;
  isCampaignModified.value = true;
  product.dropDown = false;
  product.isPromoted = true;
  isUpdating.value = true;
  isProductPromoted.value = true;
  triggerLoading($keys.KEY_NAMES.PRODUCT_PROMOTED);
  isProductAdded.value = false;

  promotedIngredientMatchData.value.unshift(product);
  promotedGTINS.value.unshift(product.gtin);
  totalPromoted.value = promotedGTINS.value.length;

  if (ingredientProductMatchesList.value.length <= 1 && currentPage.value > 1) {
    fromProduct.value -= sizeProduct.value;
    currentPage.value -= 1;
  }

  if (productTotal.value === 1) {
    resetSearchBrand();
  }

  getIngredientProductMatchesAsync();

  if (!isSearchMode.value) {
    ingredientProductMatchesList.value.splice(index, 1);
    productTotal.value -= 1;

    if (productTotal.value <= 0 || displayBrandName.value === "") {
      getBrandDistributionAsync();
    }
  }

  delayTimerPopupAsync();
  isUpdating.value = false;
  isProductAdded.value = false;
  isProductPromoted.value = false;

  const promotedProductSection = document.getElementById(
    "promoted-matches-ingredient-head"
  );
  if (promotedProductSection) {
    const headerOffset = 85;
    const elementPosition = promotedProductSection.offsetTop;
    const offsetPosition = elementPosition - headerOffset;
    document.documentElement.scrollTop = offsetPosition;
    document.body.scrollTop = offsetPosition;
  }

  triggerLoading("PRODUCT_PROMOTED");
};
const handleDragPromoted = (event) => {
  if (event.added) {
    isCampaignModified.value = true;
    isProductPromoted.value = true;
    promotedGTINS.value.splice(
      event.added.newIndex,
      0,
      event.added.element.gtin
    );
    totalPromoted.value = promotedGTINS.value.length;

    if (!isSearchMode.value) {
      productTotal.value -= 1;

      if (productTotal.value <= 0 || displayBrandName.value === "") {
        resetSearchBrand();
      }
    }

    delayTimerPopupAsync();
    isProductPromoted.value = false;
  } else if (event.moved) {
    isCampaignModified.value = true;
    promotedGTINS.value.splice(event.moved.oldIndex, 1);
    promotedGTINS.value.splice(
      event.moved.newIndex,
      0,
      event.moved.element.gtin
    );
  }
};

const addProduct = (product) => {
  isCampaignModified.value = true;
  product.isAdded = true;
  isProductAdded.value = true;
  triggerLoading("PRODUCT_ADDED");
  isProductPromoted.value = false;

  const idx = filteredGTINS.value.indexOf(product.gtin);
  if (idx !== -1) {
    filteredGTINS.value.splice(idx, 1);
  } else if (!includedGTINS.value.includes(product.gtin)) {
    includedGTINS.value.push(product.gtin);
    getIngredientProductMatchesAsync();
  }

  delayTimerPopupAsync();
  isProductAdded.value = false;
  isProductPromoted.value = false;
};

const deletePopupVisible = (ingredient) => {
  if (selectedProducts.value.length === 0) {
    deleteProductData.value = ingredient;
    isDeleteModalVisible.value = true;
  } else {
    isDeleteModalVisible.value = true;
  }
};


const delayTimerPopupAsync = async() => {
  await delay(600);
};

const deleteProduct = () => {
  isCampaignModified.value = true;
  isDeleteModalVisible.value = false;
  const product = deleteProductData.value;

  const includedIdx = includedGTINS.value.indexOf(product.gtin);
  if (includedIdx !== -1) {
    includedGTINS.value.splice(includedIdx, 1);
  } else if (!filteredGTINS.value.includes(product.gtin)) {
    filteredGTINS.value.push(product.gtin);
  }

  if (ingredientProductMatchesList.value.length <= 1 && currentPage.value > 1) {
    fromProduct.value -= sizeProduct.value;
    currentPage.value -= 1;
  }

  if (isSearchMode.value) {
    product.isAdded = false;
  } else {
    getIngredientProductMatchesAsync();
  }

  if (productTotal.value === 1) {
    resetSearchBrand();
  }

  triggerLoading($keys.KEY_NAMES.DELETED);
};

const updatePageUrlAsync = async (pageNo) => {
  router.push({
    path: "/edit-product-matches",
    query: {
      ...route.query,
      [QUERY_PARAM_KEY.NAMES]: singularName.value ? singularName.value : "",
      [QUERY_PARAM_KEY.PAGE]: pageNo > 1 ? pageNo : undefined,
    }
  });
};

const pageChangeAsync = async (event = searchCurrentPage.value) => {
  fromProduct.value = (event - 1) * sizeProduct.value;
  if (!hasLockIngredient.value && !hasEditIngredientName.value) {
    scrollToElement("scrollUp", 0);
  }
  await updatePageUrlAsync(event);
  await getIngredientProductMatchesAsync();

  selectionOfRecipes.value[0].isSelected = false;
  if (selectedProducts.value.length > 0) {
    selectedProducts.value.forEach((item) => {
      ingredientProductMatchesList.value.forEach((data) => {
        if (item.externalId === data.externalId) {
          data.isDeleteSelected = item.isDeleteSelected;
        }
      });
    });
    checkSelected();
  }
  isUpdating.value = false;
  hasLockIngredient.value = false;
  hasEditIngredientName.value = false;
};

const searchPageChangeAsync = async (event = searchCurrentPage.value) => {
  isIngredientMatchLoading.value = true;
  await updatePageUrlAsync(event);
  searchList.value = [];
  fromSearchProduct.value = (event - 1) * 15;
  isDataSearchLoading.value = false;
  searchList.value = await searchProductslAsync();
};

const searchProductslAsync = async () => {
  if (searchProductQuery.value && checkSearch.value) {
    copySearch.value = searchProductQuery.value;
  }

  const params = {
    country: lang.value.split("-")[1],
    q: copySearch.value,
    from: fromSearchProduct.value,
    size: sizeSearchProduct.value,
  };

  await store.dispatch("ingredient/getSearchProductsAsync", { params });
  const response = store.getters["ingredient/getSearchProducts"];
  const gtins = response?.results?.map((data) => data?.gtin);
  const alreadyAddedGtins = await getAlreadyAddedProductsAsync(gtins);

  response.results = response?.results?.map((data) => ({
    ...data,
    dropDown: false,
    isAdded: alreadyAddedGtins.includes(data?.gtin),
    isPromoted: promotedGTINS.value.includes(data?.gtin),
    isSearched: false,
    isSelected: true,
    isDeleteSelected: false,
  }));

  isIngredientMatchLoading.value = false;
  searchProductsTotal.value = response.total > 1000 ? 1000 : response.total;
  isDataSearchLoading.value = true;

  return response.results;
};

const getAlreadyAddedProductsAsync = async (gtins) => {
  const payload = {
    name: singularName.value,
    matchGtins: gtins,
    includedGtins: includedGTINS.value,
    filteredGtins: filteredGTINS.value,
    onlyIncluded: isOnlyIncluded.value,
    keywords: globalKeywords.value.length > 0 ? globalKeywords.value : [],
  };
  const params = { lang: lang.value };

  await store.dispatch("ingredient/getIngredientAlreadyAddedProductsAsync", {
    payload,
    params,
  });

  return store.getters["ingredient/getIngredientAlreadyAddedProduct"];
};

const getIngredientsCampaign = async (keepCampaignData) => {
  try {
    const ingredientCampaign = await findIngredientCampaignAsync(
      singularName.value
    );

    if (
      ingredientCampaign &&
      ingredientCampaign.data &&
      !ingredientCampaign.data.recipe
    ) {
      campaignIdentifier.value = ingredientCampaign.identifier;
      campaignVersion.value = ingredientCampaign.version;

      if (!keepCampaignData) {
        formatMatchedProductsGTINS(ingredientCampaign);
        isShowOnlyPromotedProducts.value = ingredientCampaign.data.onlyPromoted;
        isOnlyIncluded.value = ingredientCampaign.data.onlyIncluded;

        let shoppable = ingredientCampaign.data.shoppableFlag ?? "";
        selectedShoppableName.value =
          shoppable === "" || shoppable === "shoppable"
            ? "Shoppable (not pantry)"
            : shoppable === "shoppablePantry"
            ? "Shoppable (pantry)"
            : "Not Shoppable";
      }
    } else {
      resetIngredientsCampaign();
    }
  } catch {
    if (!keepCampaignData) {
      resetIngredientsCampaign();
    }
  }
};

const findIngredientCampaignAsync = async (name) => {
  const params = { ingredients: name };

  try {
    await store.dispatch("ingredient/getIngredientsCampaignAsync", { params });
    const response = store.getters["ingredient/getIngredientCampaign"];

    return response?.find((campaign) => campaign.data && !campaign.data.recipe);
  } catch (e) {
    if (e?.response?.status !== 404) {
      console.error(e);
    }
    return null;
  }
};

const formatMatchedProductsGTINS = async (response) => {
  includedGTINS.value = response.data.includedProducts || [];
  filteredGTINS.value = response.data.filteredProducts || [];
  promotedGTINS.value = response.data.promotedProducts || [];

  promotedIngredientMatchData.value = [];
  if (promotedGTINS?.value?.length > 0) {
    const productResponse = await getProducts(response?.targets?.[0], promotedGTINS.value);
    promotedIngredientMatchData.value = productResponse.products || [];
    totalPromoted.value = promotedIngredientMatchData.value.length;
  }
  isIngredientPromoteLoading.value = false;
};

const getProductName = (product) => {
  if (
    !product.brand ||
    product.name.toLowerCase().startsWith(product.brand.toLowerCase())
  ) {
    return product.name;
  }
  return `${product.brand} ${product.name}`;
};

const getProductId = (product) => {
  if (
    ["e_heb", "heb_sandbox", "central_market", "joev"].includes(
      store.state?.userData?.project?.id
    )
  ) {
    return product.externalId || product.gtin || "";
  }
  return product.gtin;
};

const setRecipePage = (page) => {
  recipePage.value = page;
  getRecipesAsync();
};
const getRecipesAsync = async () => {
  const params = {
    country: lang.value.split("-")[1],
    custom: isCustom.value,
    q: singularName.value,
    from: recipePage.value * recipePageSize.value,
    size: recipePageSize.value,
  };

  try {
    await store.dispatch("ingredient/getIngredientAsync", { params });
    const response = store.getters["ingredient/getIngredients"];

    if (response?.recipes?.length) {
      shortestIngredientName.value = "";
      ingredientMultipleNameList.value = [];
      ingredientNameListLength.value = "";
      defaultIngredientList.value = [];
      finalIngredientList.value = [];
      tempingIndex.value = "";
      temporaryIngredient.value = "";
      shortestIngredientName.value = response?.ingredients[0] ?? "";

      ingredientNames.value = response.ingredients ?? [];
      if (ingredientNames.value.length) {
        const sortedIngredients = ingredientNames.value.sort(
          (a, b) => a.length - b.length
        );
        shortestIngredientName.value = sortedIngredients[0];

        defaultIngredientList.value = ingredientNames.value;
        defaultIngredientList.value.forEach((item) => {
          finalIngredientList.value.push({
            name: item,
            value: "",
            note: "",
          });
        });

        showViewAll.value = true;
        ingredientMultipleNameList.value = ingredientNames.value;
        ingredientNameListLength.value = ingredientNames.value.length;

        await getRecipeCampaignsAsync(response.recipes);
        totalRecipes.value = response.totalRecipes;
      }
    }
  } catch (e) {
    console.error(e);
  }
};

const getRecipeCampaignsAsync = async (recipes) => {
  recipeDetailsList.value = [];
  recipeEmptyCount.value = 0;
  // Reset error state
  isIngredientNotFoundError.value = false;
  ingredientNotFoundMessage.value = "";

  const params = { ingredients: singularName.value };

  try {
    await store.dispatch("ingredient/getIngredientsCampaignAsync", { params });
    const response = store.getters["ingredient/getIngredientCampaign"];

    recipes?.forEach((recipe) => {
      const foundCampaign = response.find(
        (campaign) => campaign?.data?.recipe === recipe?.isin
      );
      recipe.hasCampaign = !!foundCampaign;
    });

    recipeDetailsList.value = recipes;
    recipeEmptyCount.value =
      recipePageSize.value - (recipeDetailsList.value.length ?? 0);
  } catch (e) {
    if (e?.response?.status === 404) {
      // Handle 404 gracefully - ingredient not found
      isIngredientNotFoundError.value = true;
      ingredientNotFoundMessage.value = `The ingredient '${singularName.value}' was not found. It may have been renamed in another browser session.`;

      recipeDetailsList.value = recipes;
      recipeEmptyCount.value =
        recipePageSize.value - (recipeDetailsList.value.length || 0);
    } else {
      console.error(e);
    }
  }
};

const refreshRecipeStatesAsync = async () => {
  const params = {
    country: lang.value.split("-")[1],
    custom: isCustom.value,
    q: singularName.value,
    from: recipePage.value * recipePageSize.value,
    size: recipePageSize.value,
  };

  try {
    await store.dispatch("ingredient/getIngredientAsync", { params });
    const response = store.getters["ingredient/getIngredients"];

    if (response?.recipes?.length) {
      recipeDetailsList.value.forEach((recipe) => {
        response.recipes.forEach((item) => {
          if (recipe.isin === item.isin) {
            recipe.state = item.state;
          }
        });
      });
      recipeEmptyCount.value =
        recipePageSize.value - (recipeDetailsList.value.length ?? 0);
    }
  } catch (e) {
    if (e.message === "Network Error") {
      closeModal();
    }
    console.error(e);
  }
};
const searchBrandAsync = async () => {
  if (brandSearchQuery.value.trim()) {
    isBrandSearchEnable.value = true;
    getRef("ingredientSearchBrandResetQueryIcon").style.display = "block";
    let searchedBrandedItem = [];
    isFilterBrandLoading.value = true;
    await getBrandDistributionAsync();
    getRef("ingredientSearchBrandResetQueryIcon").style.display = "block";

    searchListData.value.forEach((data) => {
      if (
        data[0].toLowerCase().includes(brandSearchQuery.value.toLowerCase())
      ) {
        searchedBrandedItem.push(data);
      }
    });

    searchListData.value = [...searchedBrandedItem];

    hasDisableAllBrandCheckbox.value = brandSearchQuery.value === "";
    if (hasDisableAllBrandCheckbox.value) {
      getRef("ingredientSearchBrandResetQueryIcon").style.display = "none";
    }

    isSelectedAllBrandChecked.value = true;
  }
  dietAutocompleteArrowCounter.value = -1;
  scrollToSelected();
};

const resetSearchBrand = () => {
  getBrandDistributionAsync();
  resetSearchBrandFields();
  currentPage.value = 1;
  pageChangeAsync(currentPage.value);
};

const resetSearchBrandFields = () => {
  isSearchBrandExitEnable.value = false;
  brandSearchQuery.value = "";
  displayBrandName.value = "";
  selectedBrand.value = "";
  fromProduct.value = 0;
  isFilterBrandPopupVisible.value = false;
};

const resetSearchQuery = () => {
  isBrandSearchEnable.value = false;
  getRef("ingredientSearchBrandResetQueryIcon").style.display = "none";
  brandSearchQuery.value = "";
  searchListData.value = [];
  fromProduct.value = 0;
  isFilterBrandLoading.value = true;
  hasDisableAllBrandCheckbox.value = true;

  if (selectedBrand.value !== "") {
    isSelectedAllBrandChecked.value = false;
  }
  getBrandDistributionAsync();
  dietAutocompleteArrowCounter.value = -1;
  scrollToSelected();
};

const getBrandDistributionAsync = async () => {
  const payload = {
    name: singularName.value,
    promotedGtins: promotedGTINS.value,
    includedGtins: includedGTINS.value,
    filteredGtins: filteredGTINS.value,
    onlyIncluded: isOnlyIncluded.value,
    keywords: globalKeywords.value.length ? globalKeywords.value : [],
  };
  const params = { lang: lang.value };

  try {
    await store.dispatch("ingredient/getBrandDistributionAsync", {
      params,
      payload,
    });
    const response = store.getters["ingredient/getBrandDistribution"];

    if (response?.ingredient === singularName.value && response?.products) {
      allBrandQuantity.value = response.products.total ?? 0;
      searchListData.value = response.products.brandDistribution
        ? Object.entries(response.products.brandDistribution).sort()
        : [];

      searchListData.value.forEach((data) => {
        data.isChecked =
          data[0].toLowerCase() === selectedBrand.value.toLowerCase();
      });

      countBrand.value = searchListData.value.length;
      isFilterBrandLoading.value = false;
    }
  } catch (e) {
    console.error(e);
  }
};
const applyFilter = () => {
  isFilterBrandPopupVisible.value = false;
  brandSearchQuery.value = selectedBrand.value;
  displayBrandName.value = brandSearchQuery.value;
  fromProduct.value = 0;
  currentPage.value = 1;
  getIngredientProductMatchesAsync();
  pageChangeAsync(currentPage.value);

  isSearchBrandExitEnable.value = !!displayBrandName.value;
  if (isSelectedAllBrandChecked.value) {
    isSearchBrandExitEnable.value = false;
  }
};

const setFilterBrandPopupVisible = () => {
  if (displayBrandName.value !== "") {
    brandSearchQuery.value = "";
    searchListData.value.forEach((data) => {
      if (
        data &&
        data[0].toLowerCase() === displayBrandName.value.toLowerCase()
      ) {
        data.isChecked = true;
        selectedBrand.value = data[0];
        isSelectedAllBrandChecked.value = false;
        hasDisableAllBrandCheckbox.value = false;
      } else {
        data.isChecked = false;
      }
    });
  } else {
    resetBrandFilter();
  }

  hasEnableBrandApplyButton.value = false;
  getBrandDistributionAsync();
  if (!isFilterBrandPopupVisible.value) {
    isFilterBrandPopupVisible.value = true;
    searchListData.value.forEach((data) => {
      if (
        data &&
        data[0].toLowerCase() === displayBrandName.value.toLowerCase()
      ) {
        selectedBrand.value = data[0];
        data.isChecked = true;
        isSelectedAllBrandChecked.value = false;
        hasDisableAllBrandCheckbox.value = false;
      } else {
        data.isChecked = false;
      }
    });
    scrollToElement("scrollUp", 70);
  } else {
    isFilterBrandPopupVisible.value = false;
  }

  dietAutocompleteArrowCounter.value = -1;
  scrollToSelected();
};

const resetBrandFilter = () => {
  brandSearchQuery.value = "";
  selectedBrand.value = "";
  displayBrandName.value = "";
  isSelectedAllBrandChecked.value = true;
  isSearchBrandExitEnable.value = false;
  hasDisableAllBrandCheckbox.value = true;
};

const selectedBrandName = (info, selectedIndex) => {
  if (info.isChecked) return;

  hasEnableBrandApplyButton.value = true;
  resetSelectedBrandBackground();
  selectedBrand.value = info[0];
  searchListData.value.forEach((data, index) => {
    if (index === selectedIndex) {
      setBackgroundColor(selectedIndex, "#e9fde5");
    }
    if (data[0].toLowerCase() === selectedBrand.value.toLowerCase()) {
      data.isChecked = true;
      isSelectedAllBrandChecked.value = false;
      setBackgroundColor("selectedAllBrand", "white");
    } else {
      data.isChecked = false;
    }
  });
};

const selectedAllBrandName = () => {
  if (isSelectedAllBrandChecked.value) return;

  hasEnableBrandApplyButton.value = true;
  resetSelectedBrandBackground();
  setBackgroundColor("selectedAllBrand", "#e9fde5");
  selectedBrand.value = "";
  isSelectedAllBrandChecked.value = !isSelectedAllBrandChecked.value;

  searchListData.value.forEach((data) => {
    data.isChecked = false;
    isSelectedAllBrandChecked.value = true;
  });
};
const getIngredientProductMatchesAsync = async () => {
  const payload = {
    name: singularName.value,
    promotedGtins: promotedGTINS.value,
    includedGtins: includedGTINS.value,
    filteredGtins: filteredGTINS.value,
    onlyIncluded: isOnlyIncluded.value,
    brand: displayBrandName.value ? displayBrandName.value : null,
    keywords: globalKeywords.value ? globalKeywords.value : [],
  };
  const params = {
    lang: lang.value,
    from: fromProduct.value || 0,
    size: sizeProduct.value,
  };

  try {
    isDataLoading.value = true;
    await store.dispatch("ingredient/getIngredientProductMatchesAsync", {
      params,
      payload,
    });
    const response = store.getters["ingredient/getIngredientsProduct"];
    isUpdating.value = false;

    if (
      response?.ingredient === singularName.value &&
      response?.products?.results
    ) {
      const products = response.products.results.map((data) => ({
        ...data,
        dropDown: false,
        isAdded: false,
        isSearched: false,
        isSelected: true,
        isPromoted: false,
        isDeleteSelected: false,
      }));
      ingredientProductMatchesList.value = products;
      productTotal.value = response.products.total ?? 0;
    }
  } catch (e) {
    console.error(e);
  } finally {
    isPageLoading.value = false;
    getBrandDistributionAsync();
    isIngredientMatchLoading.value = false;
    isDataLoading.value = false;
    triggerLoading($keys.KEY_NAMES.ROUTE_LOADING, isPageLoading.value);
  }
};

const saveRecipeCampaigns = () => {
  if (recipeIngredient.value?.hasOverridingCampaign) {
    const {
      name: ingredient,
      campaignData: {
        promotedProducts,
        includedProducts,
        filteredProducts,
        onlyPromoted,
        onlyIncluded,
        shoppableFlag,
      },
      hasLabels = [],
    } = recipeIngredient.value;

    const payload = {
      ingredient,
      recipe: recipeID.value,
      promotedProducts,
      includedProducts,
      filteredProducts,
      onlyPromoted,
      onlyIncluded,
      shoppableFlag,
      hasLabels,
    };

    const params = {
      applyToRecipeCampaigns: true,
      user: user?.email ?? "",
    };

    return store
      .dispatch("ingredient/saveIngredientCampaignDataAsync", {
        params,
        payload,
      })
      .then(() => closeModal())
      .catch((error) => {
        console.error("Error saving ingredient campaign data:", error);
      });
  }
};

const scrollToElement = (refName, headerOffset) => {
  let element = getRef(refName);
  let elementPosition = element?.offsetTop;
  let offsetPosition = elementPosition - headerOffset;
  document.documentElement.scrollTop = offsetPosition;
  document.body.scrollTop = offsetPosition;
};

const resetSelectedBrandBackground = () => {
  searchListData.value.forEach((_, index) => {
    setBackgroundColor(index, "white");
  });
};

const setBackgroundColor = (index, color) => {
  let element = getRef("selected" + index);
  if (element?.style) {
    element.style.backgroundColor = color;
  }
};

const displayOption = (item) => {
  dropdownItem.value = item;
  item.dropDown = !item.dropDown;
  if (ingredientProductMatchesList.value) {
    ingredientProductMatchesList.value.forEach((data) => {
      if (item.gtin !== data.gtin) {
        data.dropDown = false;
      }
    });
  }
  if (promotedIngredientMatchData.value) {
    promotedIngredientMatchData.value.forEach((data) => {
      if (item.gtin !== data.gtin) {
        data.dropDown = false;
      }
    });
  }
};

const editSingularNameChanged = (index, value) => {
  tempingIndex.value = index;
  temporaryIngredient.value = value;
  isSingularNameExists.value = false;
  hasEditSingularNameChecked.value = false;
  const element = getRef(`editIngloader${tempingIndex.value}`);
  if (element) {
    element.style.visibility = temporaryIngredient.value.trim()
      ? "visible"
      : "hidden";
  }
  if (temporaryIngredient.value.trim()) {
    editSingularNameHandlerAsync();
  }
  applyBtn.value = finalIngredientList.value.every(
    (item) => item.value.trim() !== ""
  );
};

const editSingularNameHandlerAsync = async () => {
  if (!temporaryIngredient.value.trim()) {
    return;
  }
  if (await checkIngredientExistAsync(temporaryIngredient.value.trim())) {
    isSingularNameExists.value = true;
  }
  const element = getRef(`editIngloader${tempingIndex.value}`);
  if (element) {
    element.style.visibility = "hidden";
  }
  hasEditSingularNameChecked.value = true;
};

const editIngredientName = () => {
  editIngredientNameConfirmedAsync();
};

const editIngredientNameConfirmedAsync = async () => {
  const updates = finalIngredientList.value
    .map((item) => ({
      name: item.name,
      updatedName: item.value,
      notes: item.note.trim() || undefined,
    }))
    .filter((data) => data);
  const payload = {
    sourceId: 210020,
    lang: lang.value,
    updates: updates,
  };
  hasEditIngredientPopup.value = true;
  isNameModalVisible.value = false;
  hasEditIngredientLoader.value = true;
  isIngredientMatchLoading.value = true;
  isIngredientPromoteLoading.value = true;
  try {
    await store.dispatch("ingredient/postUpdateIngredientNameAsync", {
      payload,
    });
    const response = store.getters["ingredient/getUpdateIngredientsName"];
    await waitOperationCompletedAsync(response?.opId);
    await delay(1000);
    finalIngredientList.value.forEach((item) => {
      item.name = item.value;
      delete item.value;
    });
    if (finalIngredientList.value.length < 1) {
      finalIngredientList.value.sort((a, b) => a.name.length - b.name.length);
    }
    singularName.value = finalIngredientList.value[0]?.name || "";
    campaignIdentifier.value = "";
    campaignVersion.value = "";
    await resetQuery();
    await resetEditIngredientName();
    await getRecipesAsync();
    await resetSearchBrand();
    await getIngredientsCampaign(false);
    globalKeywords.value = [];
    await checkForIngredientsAsync();
    isCampaignModified.value = false;
    triggerLoading(
      t("COMMON.CAMPAIGN_MODIFIED"),
      isCampaignModified.value
    );
    pageChangeAsync(currentPage.value);
  } catch (e) {
    console.error(e);
    resetEditIngredientName();
    isIngredientMatchLoading.value = false;
    isIngredientPromoteLoading.value = false;
  }
};

const resetEditIngredientName = () => {
  hasEditIngredientLoader.value = false;
  hasEditIngredientPopup.value = false;
  isNameModalVisible.value = false;
  isIngredientPromoteLoading.value = false;
};
const waitOperationCompletedAsync = async (operationId) => {
  let operationFinished = false;
  while (!operationFinished) {
    await store.dispatch("ingredient/getOperationStatusAsync", {
      operationId,
    });
    const response = store.getters["ingredient/getOperationStatus"];
    if (response.state === "done" || response.state === "failed") {
      operationFinished = true;
    }
  }
};
const checkIngredientExistAsync = async (ingredientName) => {
  if (!ingredientName) {
    return false;
  }
  const params = {
    country: lang.value.split("-")[1],
    custom: isCustom.value,
    q: ingredientName,
    from: 0,
    size: 1,
  };
  await store.dispatch("ingredient/getIngredientAsync", {
    params,
  });
  const response = store.getters["ingredient/getIngredients"];
  return response?.ingredients?.length > 0;
};
const cancelPopup = () => {
  isIngredientNameListPopupModal.value = false;
  globalKeywords.value = [...tempKeyword.value];
  isUpdating.value = false;
};

const displayPopup = () => {
  isSaveModalVisible.value = true;
};

const closeModalonEsc = () => {
  isSelectDeleteModalVisible.value = false;

  if (isIngredientNameListPopupModal.value) {
    inputKeyword.value = "";
    globalKeywords.value = [...tempKeyword.value];
  }

  isSaveModalVisible.value = false;
  isDeleteModalVisible.value = false;
  isConfirmModalVisible.value = false;

  if (!isProductTag.value) {
    isShopPreviewPopup.value = false;
    isProductTag.value = false;
  }

  isNameModalVisible.value = false;

  if (!hasEditIngredientLoader.value) {
    hasEditIngredientPopup.value = false;
  }

  if (!isIngredientNameSaving.value) {
    isIngredientNameListPopupModal.value = false;
  }
};

const closeModal = () => {
  isSelectDeleteModalVisible.value = false;
  isSaveModalVisible.value = false;
  isDeleteModalVisible.value = false;
  isPublishSuccessfully.value = false;
  isConfirmModalVisible.value = false;
  isShowCannotUpdateRecipe.value = false;
  hasEditIngredientPopup.value = false;
  resetEditIngredientName();
  isShopPreviewPopup.value = false;
  isIngredientNameListPopupModal.value = false;
  isPopupSaved.value = false;
  isProductAdded.value = false;
  isProductPromoted.value = false;
};

const dismissErrorMessage = () => {
  isIngredientNotFoundError.value = false;
  ingredientNotFoundMessage.value = "";
};

const handleClickOutside = (event) => {
  if (dropdownItem.value && dropdownItem.value.dropDown) {
    if (!document.querySelector(".menu-selected")?.contains(event.target)) {
      dropdownItem.value.dropDown = false;
    }
  }
  if (shoppableDropdownResult.value) {
    if (
      !document.querySelector(".shoppable-drop-down").contains(event.target)
    ) {
      shoppableDropdownResult.value = false;
    }
  }
};
const backToIngredients = () => {
  if (isCampaignModified.value) {
    isConfirmModalVisible.value = true;
  } else {
    backToIngredientsConfirm();
  }
};

const backToIngredientsConfirm = () => {
  router.push({
    path: "/ingredients",
    query: {
      [QUERY_PARAM_KEY.PAGE]: route.query[QUERY_PARAM_KEY.BACK_FROM],
      [QUERY_PARAM_KEY.SEARCH]: route.query[QUERY_PARAM_KEY.SEARCH],
    }
  });
  isCampaignModified.value = false;
};

const saveButtonClickAsync = async () => {
  searchProductQuery.value = "";
  isIngredientSaving.value = true;

  await postIngredientAsync();
  isCampaignModified.value = false;

  isSaveModalVisible.value = false;
  isIngredientSaving.value = false;

  let count = 0;
  if (copyKeywords.value.length === globalKeywords.value.length) {
    if (copyKeywords.value.length && globalKeywords.value.length) {
      globalKeywords.value.forEach((item) => {
        if (!copyKeywords.value.includes(item)) {
          count++;
        }
      });
      if (count > 0) {
        postKeywordData();
      }
    }
  } else {
    postKeywordData();
  }

  backToIngredientsConfirm();
};
const postIngredientAsync = async () => {
  const payload = {
    ingredient: singularName.value,
    promotedProducts: promotedGTINS.value,
    filteredProducts: filteredGTINS.value,
    includedProducts: includedGTINS.value,
    onlyPromoted: isShowOnlyPromotedProducts.value,
    onlyIncluded: isOnlyIncluded.value,
    shoppableFlag: getShoppableKey(),
  };

  const params = {
    applyToRecipeCampaigns: true,
    user: store.state.auth?.user?.email ?? "",
  };

  try {
    await store.dispatch("ingredient/saveIngredientCampaignDataAsync", {
      params,
      payload,
    });

    const response = store.getters["ingredient/getIngredientData"];
    campaignVersion.value = response.version;

    backToIngredientsConfirm();
  } catch (error) {
    showLoader.value = false;
    isIngredientSaving.value = false;
    console.error(error);
  }
};

const getProducts = async (ingredientName, gtins) => {
  const payload = {
    name: ingredientName,
    promotedGtins: gtins,
  };

  const params = {
    lang: lang.value,
    from: totalPromoted.value,
    size: gtins.length,
  };

  try {
    await store.dispatch("shoppableReview/getIngredientPromotedProductsAsync", { params, payload });

    const results = store.getters["shoppableReview/getIngredientPromotedProducts"]?.products?.results || [];

    const products = results.map(product => ({
      ...product,
      dropDown: false,
      userAdded: false,
    }));

    return { products };
  } catch (error) {
    showLoader.value = false;
    console.error(error);
  }
};
const getShoppableKey = () => {
  if (selectedShoppableName.value === "Shoppable (not pantry)") {
    return "shoppable";
  } else if (selectedShoppableName.value === "Shoppable (pantry)") {
    return "shoppablePantry";
  } else if (selectedShoppableName.value === "Not Shoppable") {
    return "nonShoppable";
  }
  return "shoppable";
};
const handleClickOutsideBrandFilterPopup = (event) => {
  if (isFilterBrandPopupVisible.value) {
    if (!document.querySelector(".brand-details").contains(event.target)) {
      isFilterBrandPopupVisible.value = false;
    }
    if (
      document
        .querySelector(".ingredient-search-brand-main")
        .contains(event.target)
    ) {
      isFilterBrandPopupVisible.value = true;
    }
  }
};

const toggleSwitch = () => {
  isCampaignModified.value = true;
  isShowOnlyPromotedProducts.value = !isShowOnlyPromotedProducts.value;
};

const debounce = (callback, delay) => {
  let timeout;
  return function () {
    clearTimeout(timeout);
    timeout = setTimeout(callback, delay);
  };
};

onUpdated(() => {
  triggerLoading(
    $keys.KEY_NAMES.CAMPAIGN_MODIFIED,
    isCampaignModified.value
  );
  hasDraggableTooltipDisplay.value = isDrag.value;
});

const cutRatingStringProduct = computed(() => {
  return (string) => string.replace("Match", "");
});

const checkSelectIngredient = computed(() => {
  selectedCount.value = selectedProducts.value.reduce((count, item) => {
    return count + (item.isDeleteSelected ? 1 : 0);
  }, 0);
  return selectedCount.value;
});

const ingredientProductStyle = computed(() => {
  if (!isFilterBrandPopupVisible.value || ingredientProductMatchesList.value.length >= 6) {
    return {};
  }

  const length = ingredientProductMatchesList.value.length;
  const marginBottom = 400 - Math.min(length, 6) * 50;

  return { marginBottom: `${marginBottom}px` };
});

const shouldShowFilterSection = computed(() => {
  return (
    selectedShoppableName.value !== "Not Shoppable" &&
    countBrand.value > 0 &&
    !isSearchMode.value &&
    !isIngredientMatchLoading.value &&
    allBrandQuantity.value !== 0 &&
    !isSelectionEnabled.value
  );
});

watch(isCampaignModified, (newValue) => {
  triggerLoading($keys.KEY_NAMES.CAMPAIGN_MODIFIED, newValue);
});
watch(() => recipeDetailsList, (list) => {
  titleRefs.value = new Array(list.length).fill(null);
  nextTick(() => {
    checkAllTitleOverflows();
  });
}, { immediate: true, deep: true });

</script>
