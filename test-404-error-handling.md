# Test Plan for 404 Error Handling in Edit Ingredient Page

## Overview
This document outlines how to test the 404 error handling implementation for the "Edit Ingredient" page when an ingredient is renamed in one browser and accessed from another browser.

## Changes Made

### 1. Store Action Update (`src/store/ingredient.js`)
- Modified `getIngredientsCampaignAsync` action to re-throw errors so they can be caught by the component
- This allows the Vue component to handle specific error types (like 404) appropriately

### 2. Vue Component Updates (`src/pages/edit-product-matches.vue`)
- Added reactive variables for error state:
  - `isIngredientNotFoundError` - boolean to control error message visibility
  - `ingredientNotFoundMessage` - string containing the error message
- Updated `getRecipeCampaignsAsync` function to:
  - Reset error state at the beginning
  - Catch 404 errors specifically
  - Set appropriate error message and state
  - Continue with normal flow (show recipes without campaign data)
- Added error message display in template with close functionality
- Added `dismissErrorMessage` function to allow users to close the error

### 3. CSS Styling (`src/assets/scss/_edit-product-matches.scss`)
- Added `.ingredient-not-found-error` class with styling consistent with existing error patterns
- Includes warning icon, message text, and close button
- Uses existing color variables for consistency

## How to Test

### Setup
1. Open the same ingredient in two different browsers (Chrome and Firefox)
2. Make sure both browsers are on the "Edit Ingredient" page for the same ingredient

### Test Steps
1. **In Chrome:**
   - Change the ingredient name (e.g., from "water" to "salted water")
   - Save the changes (POST API call succeeds)

2. **In Firefox:**
   - Try to make any change that triggers the `getRecipeCampaignsAsync` function
   - This happens when the page loads recipe data or when certain actions are performed

### Expected Results
1. **Before the fix:**
   - Firefox would get a 404 error
   - The page would break or show unexpected behavior
   - No user-friendly error message

2. **After the fix:**
   - Firefox gets a 404 error but handles it gracefully
   - An error message appears at the top of the page: "The ingredient 'water' was not found. It may have been renamed in another browser session."
   - The page continues to function normally
   - Recipes are displayed without campaign data
   - User can dismiss the error message by clicking the close (X) button

### Error Message Appearance
- Light pink background with pink border (consistent with other error messages)
- Warning icon on the left
- Clear, user-friendly message text
- Close button (X) on the right
- Message is dismissible

## Technical Details

### Error Flow
1. User action triggers `getRecipeCampaignsAsync`
2. Function calls `store.dispatch("ingredient/getIngredientsCampaignAsync")`
3. Store action makes API call with old ingredient name
4. API returns 404 (ingredient not found)
5. Store action re-throws the error
6. Component catches the error, checks if it's a 404
7. If 404: sets error state and shows user-friendly message
8. If other error: logs to console as before

### Fallback Behavior
- Even when 404 occurs, the page continues to function
- Recipes are displayed normally (without campaign data)
- User can continue using the page
- Error message provides context about what happened

## Browser Compatibility
- Works in all modern browsers (Chrome, Firefox, Safari, Edge)
- Error message styling is responsive
- Close functionality works with both click and keyboard navigation
